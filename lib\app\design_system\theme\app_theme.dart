import 'package:flutter/material.dart';
import 'color_scheme.dart';
import 'typography.dart';
import 'spacing.dart';

/// Main theme configuration for SOD-MBG application
/// Implements Material Design 3 with custom color scheme and typography
class AppTheme {
  AppTheme._();

  /// Light theme configuration
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: AppColorScheme.lightColorScheme,
      textTheme: AppTypography.textTheme,
      appBarTheme: _appBarTheme(AppColorScheme.lightColorScheme),
      elevatedButtonTheme: _elevatedButtonTheme(
        AppColorScheme.lightColorScheme,
      ),
      outlinedButtonTheme: _outlinedButtonTheme(
        AppColorScheme.lightColorScheme,
      ),
      textButtonTheme: _textButtonTheme(AppColorScheme.lightColorScheme),
      inputDecorationTheme: _inputDecorationTheme(
        AppColorScheme.lightColorScheme,
      ),
      cardTheme: _cardTheme(AppColorScheme.lightColorScheme),
      navigationRailTheme: _navigationRailTheme(
        AppColorScheme.lightColorScheme,
      ),
      bottomNavigationBarTheme: _bottomNavigationBarTheme(
        AppColorScheme.lightColorScheme,
      ),
      snackBarTheme: _snackBarTheme(AppColorScheme.lightColorScheme),
      dialogTheme: _dialogTheme(AppColorScheme.lightColorScheme),
    );
  }

  /// Dark theme configuration
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: AppColorScheme.darkColorScheme,
      textTheme: AppTypography.textTheme,
      appBarTheme: _appBarTheme(AppColorScheme.darkColorScheme),
      elevatedButtonTheme: _elevatedButtonTheme(AppColorScheme.darkColorScheme),
      outlinedButtonTheme: _outlinedButtonTheme(AppColorScheme.darkColorScheme),
      textButtonTheme: _textButtonTheme(AppColorScheme.darkColorScheme),
      inputDecorationTheme: _inputDecorationTheme(
        AppColorScheme.darkColorScheme,
      ),
      cardTheme: _cardTheme(AppColorScheme.darkColorScheme),
      navigationRailTheme: _navigationRailTheme(AppColorScheme.darkColorScheme),
      bottomNavigationBarTheme: _bottomNavigationBarTheme(
        AppColorScheme.darkColorScheme,
      ),
      snackBarTheme: _snackBarTheme(AppColorScheme.darkColorScheme),
      dialogTheme: _dialogTheme(AppColorScheme.darkColorScheme),
    );
  }

  /// AppBar theme configuration
  static AppBarTheme _appBarTheme(ColorScheme colorScheme) {
    return AppBarTheme(
      backgroundColor: colorScheme.surface,
      foregroundColor: colorScheme.onSurface,
      elevation: 0,
      scrolledUnderElevation: 1,
      centerTitle: false,
      titleTextStyle: AppTypography.textTheme.headlineSmall?.copyWith(
        color: colorScheme.onSurface,
        fontWeight: FontWeight.w500,
      ),
    );
  }

  /// Elevated button theme configuration
  static ElevatedButtonThemeData _elevatedButtonTheme(ColorScheme colorScheme) {
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        minimumSize: const Size(120, 48),
        padding: EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.sm,
        ),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        textStyle: AppTypography.textTheme.labelLarge,
      ),
    );
  }

  /// Outlined button theme configuration
  static OutlinedButtonThemeData _outlinedButtonTheme(ColorScheme colorScheme) {
    return OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: colorScheme.primary,
        minimumSize: const Size(120, 48),
        padding: EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.sm,
        ),
        side: BorderSide(color: colorScheme.primary),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        textStyle: AppTypography.textTheme.labelLarge,
      ),
    );
  }

  /// Text button theme configuration
  static TextButtonThemeData _textButtonTheme(ColorScheme colorScheme) {
    return TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: colorScheme.primary,
        minimumSize: const Size(120, 48),
        padding: EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.sm,
        ),
        textStyle: AppTypography.textTheme.labelLarge,
      ),
    );
  }

  /// Input decoration theme configuration
  static InputDecorationTheme _inputDecorationTheme(ColorScheme colorScheme) {
    return InputDecorationTheme(
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: colorScheme.outline),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: colorScheme.outline),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: colorScheme.primary, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: colorScheme.error),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: colorScheme.error, width: 2),
      ),
      filled: true,
      fillColor: colorScheme.surface,
      contentPadding: EdgeInsets.symmetric(
        horizontal: AppSpacing.md,
        vertical: AppSpacing.sm,
      ),
      labelStyle: AppTypography.textTheme.bodyMedium?.copyWith(
        color: colorScheme.onSurfaceVariant,
      ),
      hintStyle: AppTypography.textTheme.bodyMedium?.copyWith(
        color: colorScheme.onSurfaceVariant.withValues(alpha: 0.6),
      ),
    );
  }

  /// Card theme configuration
  static CardThemeData _cardTheme(ColorScheme colorScheme) {
    return CardThemeData(
      color: colorScheme.surface,
      elevation: 2,
      shadowColor: colorScheme.shadow,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      margin: EdgeInsets.all(AppSpacing.sm),
    );
  }

  /// Navigation rail theme configuration
  static NavigationRailThemeData _navigationRailTheme(ColorScheme colorScheme) {
    return NavigationRailThemeData(
      backgroundColor: colorScheme.surface,
      selectedIconTheme: IconThemeData(color: colorScheme.primary, size: 24),
      unselectedIconTheme: IconThemeData(
        color: colorScheme.onSurfaceVariant,
        size: 24,
      ),
      selectedLabelTextStyle: AppTypography.textTheme.labelMedium?.copyWith(
        color: colorScheme.primary,
        fontWeight: FontWeight.w500,
      ),
      unselectedLabelTextStyle: AppTypography.textTheme.labelMedium?.copyWith(
        color: colorScheme.onSurfaceVariant,
      ),
    );
  }

  /// Bottom navigation bar theme configuration
  static BottomNavigationBarThemeData _bottomNavigationBarTheme(
    ColorScheme colorScheme,
  ) {
    return BottomNavigationBarThemeData(
      backgroundColor: colorScheme.surface,
      selectedItemColor: colorScheme.primary,
      unselectedItemColor: colorScheme.onSurfaceVariant,
      selectedLabelStyle: AppTypography.textTheme.labelSmall?.copyWith(
        fontWeight: FontWeight.w500,
      ),
      unselectedLabelStyle: AppTypography.textTheme.labelSmall,
      type: BottomNavigationBarType.fixed,
    );
  }

  /// SnackBar theme configuration
  static SnackBarThemeData _snackBarTheme(ColorScheme colorScheme) {
    return SnackBarThemeData(
      backgroundColor: colorScheme.inverseSurface,
      contentTextStyle: AppTypography.textTheme.bodyMedium?.copyWith(
        color: colorScheme.onInverseSurface,
      ),
      actionTextColor: colorScheme.inversePrimary,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      behavior: SnackBarBehavior.floating,
    );
  }

  /// Dialog theme configuration
  static DialogThemeData _dialogTheme(ColorScheme colorScheme) {
    return DialogThemeData(
      backgroundColor: colorScheme.surface,
      surfaceTintColor: colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      titleTextStyle: AppTypography.textTheme.headlineSmall?.copyWith(
        color: colorScheme.onSurface,
      ),
      contentTextStyle: AppTypography.textTheme.bodyMedium?.copyWith(
        color: colorScheme.onSurfaceVariant,
      ),
    );
  }
}
