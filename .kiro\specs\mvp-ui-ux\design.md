# Do<PERSON>men Desain MVP UI/UX

## G<PERSON><PERSON>n Umum

Desain MVP UI/UX untuk SOD-MBG berfokus pada penciptaan sistem antarmuka yang modern, intuitif, dan dapat diakses menggunakan Material Design 3 dari Flutter. Desain ini mengutamakan pengalaman pengguna yang konsisten di semua platform (Windows desktop, Android, iOS, dan Web) dengan mempertimbangkan kebutuhan khusus staf dapur yang bekerja dalam lingkungan operasional yang cepat.

## Arsitektur

### Sistem Desain Terpusat
```
lib/app/design_system/
├── theme/
│   ├── app_theme.dart          # Konfigurasi tema utama
│   ├── color_scheme.dart       # Skema warna aplikasi
│   ├── typography.dart         # Sistem tipografi
│   └── spacing.dart           # Sistem spasi konsisten
├── components/
│   ├── buttons/               # Komponen tombol standar
│   ├── cards/                 # Komponen kartu
│   ├── forms/                 # Komponen form
│   ├── navigation/            # Komponen navigasi
│   └── feedback/              # Komponen umpan balik
└── tokens/
    ├── design_tokens.dart     # Token desain (warna, ukuran, dll)
    └── breakpoints.dart       # Breakpoint responsif
```

### Hierarki Visual
- **Primary Navigation**: AppBar dengan navigasi utama berbasis peran
- **Secondary Navigation**: Sidebar yang dapat dilipat untuk desktop, bottom navigation untuk mobile
- **Content Areas**: Layout grid responsif dengan kartu dan panel
- **Interactive Elements**: Floating Action Buttons, context menus, dan quick actions

## Komponen dan Antarmuka

### 1. Sistem Tema dan Warna

#### Skema Warna Utama
```dart
// Primary Colors - Hijau untuk identitas dapur/makanan
static const Color primaryGreen = Color(0xFF2E7D32);
static const Color primaryGreenLight = Color(0xFF60AD5E);
static const Color primaryGreenDark = Color(0xFF005005);

// Secondary Colors - Biru untuk aksi dan informasi
static const Color secondaryBlue = Color(0xFF1976D2);
static const Color secondaryBlueLight = Color(0xFF63A4FF);
static const Color secondaryBlueDark = Color(0xFF004BA0);

// Surface Colors - Netral untuk latar belakang
static const Color surfaceLight = Color(0xFFFAFAFA);
static const Color surfaceDark = Color(0xFF121212);
static const Color surfaceVariant = Color(0xFFF5F5F5);

// Status Colors
static const Color successGreen = Color(0xFF4CAF50);
static const Color warningOrange = Color(0xFFFF9800);
static const Color errorRed = Color(0xFFF44336);
static const Color infoBlue = Color(0xFF2196F3);
```

#### Kontras dan Aksesibilitas
- Semua kombinasi warna memenuhi standar WCAG 2.1 AA (rasio kontras minimum 4.5:1)
- Mode gelap dan terang tersedia dengan transisi yang mulus
- Indikator fokus yang jelas untuk navigasi keyboard

### 2. Sistem Tipografi

#### Hierarki Teks
```dart
// Display - Untuk judul halaman utama
displayLarge: TextStyle(fontSize: 32, fontWeight: FontWeight.w400)
displayMedium: TextStyle(fontSize: 28, fontWeight: FontWeight.w400)
displaySmall: TextStyle(fontSize: 24, fontWeight: FontWeight.w400)

// Headline - Untuk judul section
headlineLarge: TextStyle(fontSize: 22, fontWeight: FontWeight.w500)
headlineMedium: TextStyle(fontSize: 20, fontWeight: FontWeight.w500)
headlineSmall: TextStyle(fontSize: 18, fontWeight: FontWeight.w500)

// Body - Untuk konten utama
bodyLarge: TextStyle(fontSize: 16, fontWeight: FontWeight.w400)
bodyMedium: TextStyle(fontSize: 14, fontWeight: FontWeight.w400)
bodySmall: TextStyle(fontSize: 12, fontWeight: FontWeight.w400)

// Label - Untuk label dan tombol
labelLarge: TextStyle(fontSize: 14, fontWeight: FontWeight.w500)
labelMedium: TextStyle(fontSize: 12, fontWeight: FontWeight.w500)
labelSmall: TextStyle(fontSize: 10, fontWeight: FontWeight.w500)
```

### 3. Komponen Navigasi

#### AppBar Responsif
- **Desktop**: AppBar penuh dengan menu horizontal dan profil pengguna
- **Mobile**: AppBar kompak dengan hamburger menu dan ikon profil
- **Tablet**: Hybrid approach dengan navigasi yang dapat dilipat

#### Sidebar Navigation (Desktop)
```dart
// Struktur navigasi berbasis peran
NavigationRail(
  destinations: [
    NavigationRailDestination(
      icon: Icon(Icons.dashboard),
      label: Text('Dashboard'),
      permission: AdminPermission,
    ),
    NavigationRailDestination(
      icon: Icon(Icons.kitchen),
      label: Text('Operasi Dapur'),
      permission: KitchenManagementPermission,
    ),
    // ... destinasi lainnya
  ],
)
```

#### Bottom Navigation (Mobile)
- Maximum 5 item navigasi utama
- Ikon dengan label yang jelas
- Badge untuk notifikasi dan status

### 4. Komponen Kartu dan Panel

#### Dashboard Cards
```dart
class MetricCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;
  final VoidCallback? onTap;
  
  // Implementasi dengan Material 3 styling
}
```

#### Data Cards
- Elevation yang konsisten (2dp untuk kartu standar, 4dp untuk kartu yang ditinggikan)
- Corner radius 12px untuk tampilan modern
- Padding internal 16px untuk konten
- Shadow yang halus untuk depth

### 5. Komponen Form

#### Input Fields
```dart
class AppTextField extends StatelessWidget {
  final String label;
  final String? hint;
  final bool isRequired;
  final String? Function(String?)? validator;
  
  // Styling dengan Material 3 OutlinedInputBorder
}
```

#### Button System
```dart
// Primary Button - Untuk aksi utama
ElevatedButton(
  style: ElevatedButton.styleFrom(
    backgroundColor: primaryGreen,
    foregroundColor: Colors.white,
    minimumSize: Size(120, 48),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(8),
    ),
  ),
)

// Secondary Button - Untuk aksi sekunder
OutlinedButton(
  style: OutlinedButton.styleFrom(
    foregroundColor: primaryGreen,
    side: BorderSide(color: primaryGreen),
    minimumSize: Size(120, 48),
  ),
)

// Text Button - Untuk aksi tersier
TextButton(
  style: TextButton.styleFrom(
    foregroundColor: primaryGreen,
    minimumSize: Size(120, 48),
  ),
)
```

### 6. Sistem Umpan Balik

#### Loading States
```dart
class LoadingOverlay extends StatelessWidget {
  final bool isLoading;
  final Widget child;
  final String? message;
  
  // Implementasi dengan CircularProgressIndicator dan backdrop
}
```

#### Snackbar System
```dart
class AppSnackBar {
  static void showSuccess(BuildContext context, String message);
  static void showError(BuildContext context, String message);
  static void showWarning(BuildContext context, String message);
  static void showInfo(BuildContext context, String message);
}
```

#### Dialog System
```dart
class ConfirmationDialog extends StatelessWidget {
  final String title;
  final String content;
  final VoidCallback onConfirm;
  final VoidCallback onCancel;
  final bool isDestructive;
  
  // Styling dengan Material 3 AlertDialog
}
```

## Model Data

### Theme Configuration Model
```dart
class AppThemeConfig {
  final ColorScheme colorScheme;
  final TextTheme textTheme;
  final bool isDarkMode;
  final double scaleFactor;
  
  const AppThemeConfig({
    required this.colorScheme,
    required this.textTheme,
    this.isDarkMode = false,
    this.scaleFactor = 1.0,
  });
}
```

### Responsive Breakpoints
```dart
class AppBreakpoints {
  static const double mobile = 600;
  static const double tablet = 1024;
  static const double desktop = 1440;
  
  static bool isMobile(double width) => width < mobile;
  static bool isTablet(double width) => width >= mobile && width < desktop;
  static bool isDesktop(double width) => width >= desktop;
}
```

### Component State Models
```dart
class ButtonState {
  final bool isLoading;
  final bool isDisabled;
  final String? errorMessage;
  
  const ButtonState({
    this.isLoading = false,
    this.isDisabled = false,
    this.errorMessage,
  });
}
```

## Penanganan Error

### Error Display Strategy
1. **Inline Errors**: Untuk validasi form dan input errors
2. **Snackbar Errors**: Untuk operasi yang gagal dengan pesan singkat
3. **Dialog Errors**: Untuk error kritis yang memerlukan perhatian pengguna
4. **Page-level Errors**: Untuk error yang mempengaruhi seluruh halaman

### Error Message Localization
```dart
class ErrorMessages {
  static const String networkError = 'Koneksi internet bermasalah. Silakan coba lagi.';
  static const String validationError = 'Data yang dimasukkan tidak valid.';
  static const String permissionError = 'Anda tidak memiliki izin untuk melakukan aksi ini.';
  static const String serverError = 'Terjadi kesalahan pada server. Silakan coba lagi nanti.';
}
```

## Strategi Pengujian

### Unit Testing
- Test semua komponen UI dengan berbagai state
- Test responsivitas pada berbagai ukuran layar
- Test aksesibilitas dengan screen reader simulation

### Widget Testing
```dart
testWidgets('MetricCard displays correct information', (tester) async {
  await tester.pumpWidget(
    MaterialApp(
      home: MetricCard(
        title: 'Total Meals',
        value: '1,234',
        icon: Icons.restaurant,
        color: Colors.green,
      ),
    ),
  );
  
  expect(find.text('Total Meals'), findsOneWidget);
  expect(find.text('1,234'), findsOneWidget);
  expect(find.byIcon(Icons.restaurant), findsOneWidget);
});
```

### Integration Testing
- Test navigasi antar halaman
- Test interaksi form dan validasi
- Test responsive behavior pada berbagai device

### Accessibility Testing
- Test keyboard navigation
- Test screen reader compatibility
- Test color contrast ratios
- Test touch target sizes

## Implementasi Responsif

### Layout Strategy
```dart
class ResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget tablet;
  final Widget desktop;
  
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (AppBreakpoints.isMobile(constraints.maxWidth)) {
          return mobile;
        } else if (AppBreakpoints.isTablet(constraints.maxWidth)) {
          return tablet;
        } else {
          return desktop;
        }
      },
    );
  }
}
```

### Grid System
```dart
class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        int crossAxisCount;
        if (AppBreakpoints.isMobile(constraints.maxWidth)) {
          crossAxisCount = 1;
        } else if (AppBreakpoints.isTablet(constraints.maxWidth)) {
          crossAxisCount = 2;
        } else {
          crossAxisCount = 3;
        }
        
        return GridView.builder(
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
          ),
          itemCount: children.length,
          itemBuilder: (context, index) => children[index],
        );
      },
    );
  }
}
```

## Performance Optimization

### Widget Optimization
- Gunakan `const` constructors untuk widget yang tidak berubah
- Implementasi `RepaintBoundary` untuk widget yang sering di-repaint
- Gunakan `ListView.builder` untuk list yang panjang
- Implementasi lazy loading untuk data yang besar

### Asset Optimization
- Gunakan format gambar yang optimal (WebP untuk web, PNG untuk mobile)
- Implementasi multiple resolution assets (1x, 2x, 3x)
- Compress assets untuk mengurangi ukuran bundle

### Animation Performance
```dart
class OptimizedAnimation extends StatefulWidget {
  @override
  _OptimizedAnimationState createState() => _OptimizedAnimationState();
}

class _OptimizedAnimationState extends State<OptimizedAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(milliseconds: 300),
      vsync: this,
    );
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
```

## Integrasi dengan Sistem yang Ada

### Permission-Based UI
```dart
class PermissionAwareWidget extends StatelessWidget {
  final Widget child;
  final Type permissionType;
  final Widget? fallback;
  
  @override
  Widget build(BuildContext context) {
    if (context.hasPermission(permissionType)) {
      return child;
    }
    return fallback ?? SizedBox.shrink();
  }
}
```

### Theme Integration dengan BLoC
```dart
class ThemeCubit extends Cubit<ThemeState> {
  ThemeCubit() : super(ThemeState.light());
  
  void toggleTheme() {
    emit(state.isDark ? ThemeState.light() : ThemeState.dark());
  }
  
  void setTheme(bool isDark) {
    emit(isDark ? ThemeState.dark() : ThemeState.light());
  }
}
```

### Offline-First UI Considerations
- Implementasi indikator koneksi
- Caching untuk asset dan data UI
- Graceful degradation untuk fitur yang memerlukan koneksi
- Sync indicators untuk data yang pending

Desain ini memastikan konsistensi visual, aksesibilitas yang baik, dan performa optimal di semua platform yang didukung sambil mempertahankan fleksibilitas untuk pengembangan masa depan.