import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:aplikasi_sppg/app/design_system/components/navigation/app_sidebar.dart';

void main() {
  group('AppSidebar Widget Interaction Tests', () {
    testWidgets('toggles between expanded and collapsed states', (
      WidgetTester tester,
    ) async {
      // Arrange
      bool isExpanded = true;
      final items = [
        const AppSidebarItem(icon: Icon(Icons.home), label: 'Home'),
      ];

      // Act - Build the widget in expanded state
      await tester.pumpWidget(
        MaterialApp(
          home: StatefulBuilder(
            builder: (context, setState) {
              return Scaffold(
                body: AppSidebar(
                  selectedIndex: 0,
                  items: items,
                  isExpanded: isExpanded,
                  onExpandButtonPressed: () {
                    setState(() {
                      isExpanded = !isExpanded;
                    });
                  },
                ),
              );
            },
          ),
        ),
      );

      // Initial state - expanded
      expect(find.byIcon(Icons.chevron_left), findsOneWidget);

      // Tap the collapse button
      await tester.tap(find.byIcon(Icons.chevron_left));
      await tester.pumpAndSettle();

      // Should now be collapsed
      expect(find.byIcon(Icons.chevron_right), findsOneWidget);

      // Tap the expand button
      await tester.tap(find.byIcon(Icons.chevron_right));
      await tester.pumpAndSettle();

      // Should be expanded again
      expect(find.byIcon(Icons.chevron_left), findsOneWidget);
    });

    testWidgets('animates width change when toggling expanded state', (
      WidgetTester tester,
    ) async {
      // Arrange
      bool isExpanded = true;
      final items = [
        const AppSidebarItem(icon: Icon(Icons.home), label: 'Home'),
      ];

      // Act - Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: StatefulBuilder(
            builder: (context, setState) {
              return Scaffold(
                body: AppSidebar(
                  selectedIndex: 0,
                  items: items,
                  isExpanded: isExpanded,
                  expandedWidth: 280.0,
                  collapsedWidth: 80.0,
                  onExpandButtonPressed: () {
                    setState(() {
                      isExpanded = !isExpanded;
                    });
                  },
                ),
              );
            },
          ),
        ),
      );

      // Initial width should be expanded
      final initialRenderBox = tester.renderObject<RenderBox>(
        find.byType(AppSidebar).first,
      );
      expect(initialRenderBox.size.width, 280.0);

      // Tap the collapse button
      await tester.tap(find.byIcon(Icons.chevron_left));
      await tester.pump();

      // During animation, width should be changing
      await tester.pump(const Duration(milliseconds: 150));

      // After animation completes, width should be collapsed
      await tester.pumpAndSettle();
      final collapsedRenderBox = tester.renderObject<RenderBox>(
        find.byType(AppSidebar).first,
      );
      expect(collapsedRenderBox.size.width, 80.0);
    });

    testWidgets('shows/hides labels based on expanded state', (
      WidgetTester tester,
    ) async {
      // Arrange
      bool isExpanded = true;
      final items = [
        const AppSidebarItem(icon: Icon(Icons.home), label: 'Home'),
      ];

      // Act - Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: StatefulBuilder(
            builder: (context, setState) {
              return Scaffold(
                body: AppSidebar(
                  selectedIndex: 0,
                  items: items,
                  isExpanded: isExpanded,
                  header:
                      isExpanded
                          ? const Text('Expanded Header')
                          : const Text('Collapsed Header'),
                  onExpandButtonPressed: () {
                    setState(() {
                      isExpanded = !isExpanded;
                    });
                  },
                ),
              );
            },
          ),
        ),
      );

      // Initial state - expanded
      expect(find.text('Expanded Header'), findsOneWidget);

      // Tap the collapse button
      await tester.tap(find.byIcon(Icons.chevron_left));
      await tester.pumpAndSettle();

      // Should now show collapsed header
      expect(find.text('Collapsed Header'), findsOneWidget);
      expect(find.text('Expanded Header'), findsNothing);
    });

    testWidgets('selects correct item when tapped', (
      WidgetTester tester,
    ) async {
      // Arrange
      int selectedIndex = 0;
      final items = [
        const AppSidebarItem(icon: Icon(Icons.home), label: 'Home'),
        const AppSidebarItem(icon: Icon(Icons.settings), label: 'Settings'),
        const AppSidebarItem(icon: Icon(Icons.person), label: 'Profile'),
      ];

      // Act - Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: StatefulBuilder(
            builder: (context, setState) {
              return Scaffold(
                body: Row(
                  children: [
                    AppSidebar(
                      selectedIndex: selectedIndex,
                      items: items,
                      onDestinationSelected: (index) {
                        setState(() {
                          selectedIndex = index;
                        });
                      },
                    ),
                    Expanded(
                      child: Center(
                        child: Text('Selected: ${items[selectedIndex].label}'),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      );

      // Initial state
      expect(find.text('Selected: Home'), findsOneWidget);

      // Tap the settings item
      await tester.tap(find.byIcon(Icons.settings));
      await tester.pumpAndSettle();

      // Should now show settings as selected
      expect(find.text('Selected: Settings'), findsOneWidget);

      // Tap the profile item
      await tester.tap(find.byIcon(Icons.person));
      await tester.pumpAndSettle();

      // Should now show profile as selected
      expect(find.text('Selected: Profile'), findsOneWidget);
    });
  });

  group('AppSidebarScaffold Widget Tests', () {
    testWidgets('integrates sidebar with main content correctly', (
      WidgetTester tester,
    ) async {
      // Arrange
      bool isExpanded = true;
      int selectedIndex = 0;
      final items = [
        const AppSidebarItem(icon: Icon(Icons.home), label: 'Home'),
        const AppSidebarItem(icon: Icon(Icons.settings), label: 'Settings'),
      ];

      // Act - Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: StatefulBuilder(
            builder: (context, setState) {
              return AppSidebarScaffold(
                sidebar: AppSidebar(
                  selectedIndex: selectedIndex,
                  items: items,
                  isExpanded: isExpanded,
                  onDestinationSelected: (index) {
                    setState(() {
                      selectedIndex = index;
                    });
                  },
                  onExpandButtonPressed: () {
                    setState(() {
                      isExpanded = !isExpanded;
                    });
                  },
                ),
                body: Center(
                  child: Text('Content for ${items[selectedIndex].label}'),
                ),
                appBar: AppBar(title: const Text('Test App')),
              );
            },
          ),
        ),
      );

      // Assert
      expect(find.text('Test App'), findsOneWidget);
      expect(find.text('Content for Home'), findsOneWidget);

      // Tap the settings item
      await tester.tap(find.byIcon(Icons.settings));
      await tester.pumpAndSettle();

      // Should now show settings content
      expect(find.text('Content for Settings'), findsOneWidget);

      // Tap the collapse button
      await tester.tap(find.byIcon(Icons.chevron_left));
      await tester.pumpAndSettle();

      // Sidebar should be collapsed but content should remain
      expect(find.byIcon(Icons.chevron_right), findsOneWidget);
      expect(find.text('Content for Settings'), findsOneWidget);
    });
  });
}
