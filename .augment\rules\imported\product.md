---
type: "always_apply"
---

# SOD-MBG Product Guidelines

## Product Overview

SOD-MBG (Sistem Operasional Dapur MBG) is a cross-platform operational management system for kitchen operations supporting Indonesia's Free Nutritious Meal Program (Program Makan Bergizi Gratis) from the National Nutrition Agency (Badan Gizi Nasional).

## Core Purpose & Principles

- Support daily operations of SPPG (Nutrition Service Units) kitchens
- Ensure compliance with national nutrition standards
- Provide oversight tools for foundation management over both owned and partner kitchens
- Enable accurate reporting and operational efficiency
- Prioritize offline-first functionality for reliable operation in areas with limited connectivity

## Feature Implementation Guidelines

### Centralized Dashboard
- Implement modular widget architecture for performance metrics
- Use responsive design patterns for all dashboard components
- Ensure all metrics are filterable by date range and SPPG unit

### Kitchen Operations Management
- Follow step-by-step workflow patterns with clear state transitions
- Implement comprehensive validation at each operational stage
- Maintain audit trail for all kitchen operations

### Offline-First Data Management
- Use PowerSync for all data synchronization operations
- Implement optimistic UI updates with proper error recovery
- Cache critical operational data for offline functionality

### User Experience Standards
- Maintain consistent navigation patterns across all modules
- Use Fluent UI design language for all interface components
- Implement progressive disclosure for complex workflows
- Ensure all interfaces are accessible on both desktop and mobile devices

## User Role Implementation

When implementing features, respect these role-based access patterns:

1. **Admin Yayasan**
   - Full system access with administrative capabilities
   - Implementation should use `AdminPermission` mixin

2. **Perwakilan Yayasan**
   - Read access to all data with verification capabilities
   - Implementation should use `OversightPermission` mixin

3. **Kepala Dapur SPPG**
   - Full access to kitchen operations for assigned SPPG only
   - Implementation should use `KitchenManagementPermission` mixin

4. **Ahli Gizi**
   - Menu and nutrition management capabilities
   - Implementation should use `NutritionPermission` mixin

5. **Akuntan**
   - Financial transaction capabilities
   - Implementation should use `FinancialPermission` mixin

6. **Pengawas Pemeliharaan & Penghantaran**
   - Logistics and maintenance capabilities
   - Implementation should use `LogisticsPermission` mixin

## Data Model Conventions

- All entity models must extend `BaseEntity` class
- Use `@JsonSerializable()` for all data models
- Implement `toJson()` and `fromJson()` methods for all models
- Follow naming convention: `{Entity}Model` for data layer, `{Entity}Entity` for domain layer

## Error Handling Standards

- Use dedicated error types from `core/errors/`
- Implement graceful degradation for offline scenarios
- Display user-friendly error messages in Bahasa Indonesia
- Log detailed error information for debugging

## Localization Guidelines

- All user-facing text must support Bahasa Indonesia
- Use the `AppLocalizations` class for all text resources
- Follow the key format: `{module}.{submodule}.{element}`
- Maintain context comments for all translation strings

## Performance Requirements

- Dashboard must load within 2 seconds on target devices
- All list views must implement pagination
- Offline data sync must complete within 30 seconds on reconnection
- Optimize image assets for mobile devices

## Testing Requirements

- Implement unit tests for all business logic
- Create widget tests for all reusable components
- Ensure offline functionality is covered in integration tests