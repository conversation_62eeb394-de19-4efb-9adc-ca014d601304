import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:aplikasi_sppg/app/design_system/theme/spacing.dart';

void main() {
  group('AppSpacing', () {
    test('should have consistent spacing values based on base unit', () {
      // Base unit is 8.0
      const baseUnit = 8.0;

      // Check that spacing values follow the correct multipliers
      expect(AppSpacing.xs, equals(baseUnit * 0.5)); // 4px
      expect(AppSpacing.sm, equals(baseUnit * 1.0)); // 8px
      expect(AppSpacing.md, equals(baseUnit * 2.0)); // 16px
      expect(AppSpacing.lg, equals(baseUnit * 3.0)); // 24px
      expect(AppSpacing.xl, equals(baseUnit * 4.0)); // 32px
      expect(AppSpacing.xxl, equals(baseUnit * 6.0)); // 48px
      expect(AppSpacing.xxxl, equals(baseUnit * 8.0)); // 64px
    });

    test('should have component-specific spacing values', () {
      // Check that component spacing values are defined correctly
      expect(AppSpacing.cardPadding, equals(AppSpacing.md)); // 16px
      expect(AppSpacing.cardMargin, equals(AppSpacing.sm)); // 8px
      expect(AppSpacing.buttonPadding, equals(AppSpacing.md)); // 16px
      expect(AppSpacing.formFieldSpacing, equals(AppSpacing.md)); // 16px
      expect(AppSpacing.sectionSpacing, equals(AppSpacing.lg)); // 24px
      expect(AppSpacing.pageMargin, equals(AppSpacing.md)); // 16px
      expect(AppSpacing.screenPadding, equals(AppSpacing.md)); // 16px
      expect(AppSpacing.contentSpacing, equals(AppSpacing.lg)); // 24px
      expect(AppSpacing.listItemSpacing, equals(AppSpacing.sm)); // 8px
      expect(AppSpacing.gridSpacing, equals(AppSpacing.md)); // 16px
      expect(AppSpacing.navigationPadding, equals(AppSpacing.sm)); // 8px
      expect(AppSpacing.navigationItemSpacing, equals(AppSpacing.xs)); // 4px
      expect(AppSpacing.appBarPadding, equals(AppSpacing.md)); // 16px
    });

    test('should provide EdgeInsets presets for all sides', () {
      // Check that EdgeInsets presets are defined correctly
      expect(AppSpacing.allXs, equals(const EdgeInsets.all(AppSpacing.xs)));
      expect(AppSpacing.allSm, equals(const EdgeInsets.all(AppSpacing.sm)));
      expect(AppSpacing.allMd, equals(const EdgeInsets.all(AppSpacing.md)));
      expect(AppSpacing.allLg, equals(const EdgeInsets.all(AppSpacing.lg)));
      expect(AppSpacing.allXl, equals(const EdgeInsets.all(AppSpacing.xl)));
    });

    test('should provide EdgeInsets presets for horizontal and vertical', () {
      // Check horizontal EdgeInsets
      expect(
        AppSpacing.horizontalXs,
        equals(const EdgeInsets.symmetric(horizontal: AppSpacing.xs)),
      );
      expect(
        AppSpacing.horizontalSm,
        equals(const EdgeInsets.symmetric(horizontal: AppSpacing.sm)),
      );
      expect(
        AppSpacing.horizontalMd,
        equals(const EdgeInsets.symmetric(horizontal: AppSpacing.md)),
      );
      expect(
        AppSpacing.horizontalLg,
        equals(const EdgeInsets.symmetric(horizontal: AppSpacing.lg)),
      );
      expect(
        AppSpacing.horizontalXl,
        equals(const EdgeInsets.symmetric(horizontal: AppSpacing.xl)),
      );

      // Check vertical EdgeInsets
      expect(
        AppSpacing.verticalXs,
        equals(const EdgeInsets.symmetric(vertical: AppSpacing.xs)),
      );
      expect(
        AppSpacing.verticalSm,
        equals(const EdgeInsets.symmetric(vertical: AppSpacing.sm)),
      );
      expect(
        AppSpacing.verticalMd,
        equals(const EdgeInsets.symmetric(vertical: AppSpacing.md)),
      );
      expect(
        AppSpacing.verticalLg,
        equals(const EdgeInsets.symmetric(vertical: AppSpacing.lg)),
      );
      expect(
        AppSpacing.verticalXl,
        equals(const EdgeInsets.symmetric(vertical: AppSpacing.xl)),
      );
    });

    test('should provide EdgeInsets presets for specific directions', () {
      // Check top EdgeInsets
      expect(
        AppSpacing.topXs,
        equals(const EdgeInsets.only(top: AppSpacing.xs)),
      );
      expect(
        AppSpacing.topSm,
        equals(const EdgeInsets.only(top: AppSpacing.sm)),
      );
      expect(
        AppSpacing.topMd,
        equals(const EdgeInsets.only(top: AppSpacing.md)),
      );
      expect(
        AppSpacing.topLg,
        equals(const EdgeInsets.only(top: AppSpacing.lg)),
      );

      // Check bottom EdgeInsets
      expect(
        AppSpacing.bottomXs,
        equals(const EdgeInsets.only(bottom: AppSpacing.xs)),
      );
      expect(
        AppSpacing.bottomSm,
        equals(const EdgeInsets.only(bottom: AppSpacing.sm)),
      );
      expect(
        AppSpacing.bottomMd,
        equals(const EdgeInsets.only(bottom: AppSpacing.md)),
      );
      expect(
        AppSpacing.bottomLg,
        equals(const EdgeInsets.only(bottom: AppSpacing.lg)),
      );

      // Check left EdgeInsets
      expect(
        AppSpacing.leftXs,
        equals(const EdgeInsets.only(left: AppSpacing.xs)),
      );
      expect(
        AppSpacing.leftSm,
        equals(const EdgeInsets.only(left: AppSpacing.sm)),
      );
      expect(
        AppSpacing.leftMd,
        equals(const EdgeInsets.only(left: AppSpacing.md)),
      );
      expect(
        AppSpacing.leftLg,
        equals(const EdgeInsets.only(left: AppSpacing.lg)),
      );

      // Check right EdgeInsets
      expect(
        AppSpacing.rightXs,
        equals(const EdgeInsets.only(right: AppSpacing.xs)),
      );
      expect(
        AppSpacing.rightSm,
        equals(const EdgeInsets.only(right: AppSpacing.sm)),
      );
      expect(
        AppSpacing.rightMd,
        equals(const EdgeInsets.only(right: AppSpacing.md)),
      );
      expect(
        AppSpacing.rightLg,
        equals(const EdgeInsets.only(right: AppSpacing.lg)),
      );
    });

    test('should provide SizedBox presets for spacing between widgets', () {
      // Check vertical SizedBox presets
      final verticalSpaceXs = AppSpacing.verticalSpaceXs as SizedBox;
      expect(verticalSpaceXs.height, equals(AppSpacing.xs));

      final verticalSpaceSm = AppSpacing.verticalSpaceSm as SizedBox;
      expect(verticalSpaceSm.height, equals(AppSpacing.sm));

      final verticalSpaceMd = AppSpacing.verticalSpaceMd as SizedBox;
      expect(verticalSpaceMd.height, equals(AppSpacing.md));

      final verticalSpaceLg = AppSpacing.verticalSpaceLg as SizedBox;
      expect(verticalSpaceLg.height, equals(AppSpacing.lg));

      final verticalSpaceXl = AppSpacing.verticalSpaceXl as SizedBox;
      expect(verticalSpaceXl.height, equals(AppSpacing.xl));

      final verticalSpaceXxl = AppSpacing.verticalSpaceXxl as SizedBox;
      expect(verticalSpaceXxl.height, equals(AppSpacing.xxl));

      // Check horizontal SizedBox presets
      final horizontalSpaceXs = AppSpacing.horizontalSpaceXs as SizedBox;
      expect(horizontalSpaceXs.width, equals(AppSpacing.xs));

      final horizontalSpaceSm = AppSpacing.horizontalSpaceSm as SizedBox;
      expect(horizontalSpaceSm.width, equals(AppSpacing.sm));

      final horizontalSpaceMd = AppSpacing.horizontalSpaceMd as SizedBox;
      expect(horizontalSpaceMd.width, equals(AppSpacing.md));

      final horizontalSpaceLg = AppSpacing.horizontalSpaceLg as SizedBox;
      expect(horizontalSpaceLg.width, equals(AppSpacing.lg));

      final horizontalSpaceXl = AppSpacing.horizontalSpaceXl as SizedBox;
      expect(horizontalSpaceXl.width, equals(AppSpacing.xl));

      final horizontalSpaceXxl = AppSpacing.horizontalSpaceXxl as SizedBox;
      expect(horizontalSpaceXxl.width, equals(AppSpacing.xxl));
    });

    test('should provide utility methods for dynamic spacing', () {
      // Test getSpacing utility
      expect(AppSpacing.getSpacing(0.5), equals(4.0));
      expect(AppSpacing.getSpacing(1.0), equals(8.0));
      expect(AppSpacing.getSpacing(2.0), equals(16.0));
      expect(AppSpacing.getSpacing(3.0), equals(24.0));
      expect(AppSpacing.getSpacing(4.0), equals(32.0));

      // Test getResponsiveSpacing utility
      const baseSpacing = 16.0;

      // Mobile screen (< 600px)
      expect(
        AppSpacing.getResponsiveSpacing(baseSpacing, 400),
        equals(baseSpacing * 0.75),
      );

      // Tablet screen (600px - 1024px)
      expect(
        AppSpacing.getResponsiveSpacing(baseSpacing, 800),
        equals(baseSpacing),
      );

      // Desktop screen (> 1024px)
      expect(
        AppSpacing.getResponsiveSpacing(baseSpacing, 1200),
        equals(baseSpacing * 1.25),
      );
    });

    test('should provide responsive padding based on screen width', () {
      const basePadding = EdgeInsets.all(16.0);

      // Mobile screen (< 600px)
      final mobilePadding = AppSpacing.getResponsivePadding(basePadding, 400);
      expect(mobilePadding.left, equals(basePadding.left * 0.75));
      expect(mobilePadding.top, equals(basePadding.top * 0.75));
      expect(mobilePadding.right, equals(basePadding.right * 0.75));
      expect(mobilePadding.bottom, equals(basePadding.bottom * 0.75));

      // Tablet screen (600px - 1024px)
      final tabletPadding = AppSpacing.getResponsivePadding(basePadding, 800);
      expect(tabletPadding.left, equals(basePadding.left));
      expect(tabletPadding.top, equals(basePadding.top));
      expect(tabletPadding.right, equals(basePadding.right));
      expect(tabletPadding.bottom, equals(basePadding.bottom));

      // Desktop screen (> 1024px)
      final desktopPadding = AppSpacing.getResponsivePadding(basePadding, 1200);
      expect(desktopPadding.left, equals(basePadding.left * 1.25));
      expect(desktopPadding.top, equals(basePadding.top * 1.25));
      expect(desktopPadding.right, equals(basePadding.right * 1.25));
      expect(desktopPadding.bottom, equals(basePadding.bottom * 1.25));
    });

    test('should create custom EdgeInsets with responsive values', () {
      // Test responsive EdgeInsets with 'all' parameter
      final allPadding = AppSpacing.responsive(screenWidth: 400, all: 16.0);
      expect(allPadding, equals(const EdgeInsets.all(16.0 * 0.75)));

      // Test responsive EdgeInsets with 'horizontal' and 'vertical' parameters
      final symmetricPadding = AppSpacing.responsive(
        screenWidth: 800,
        horizontal: 16.0,
        vertical: 8.0,
      );
      expect(
        symmetricPadding,
        equals(const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0)),
      );

      // Test responsive EdgeInsets with individual parameters
      final customPadding = AppSpacing.responsive(
        screenWidth: 1200,
        left: 16.0,
        top: 8.0,
        right: 16.0,
        bottom: 8.0,
      );
      expect(
        customPadding,
        equals(
          EdgeInsets.only(
            left: 16.0 * 1.25,
            top: 8.0 * 1.25,
            right: 16.0 * 1.25,
            bottom: 8.0 * 1.25,
          ),
        ),
      );
    });

    test('should create SizedBox with responsive spacing', () {
      // Test responsive vertical space
      final responsiveVerticalSpace =
          AppSpacing.responsiveVerticalSpace(16.0, 400) as SizedBox;
      expect(responsiveVerticalSpace.height, equals(16.0 * 0.75));

      // Test responsive horizontal space
      final responsiveHorizontalSpace =
          AppSpacing.responsiveHorizontalSpace(16.0, 1200) as SizedBox;
      expect(responsiveHorizontalSpace.width, equals(16.0 * 1.25));
    });
  });
}
