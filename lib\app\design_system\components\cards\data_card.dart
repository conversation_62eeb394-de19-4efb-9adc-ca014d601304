import 'package:flutter/material.dart';
import '../../theme/spacing.dart';
import '../../theme/typography.dart';
import '../../tokens/breakpoints.dart';
import '../../tokens/design_tokens.dart';

/// A card widget for displaying operational information in various layouts.
///
/// This widget displays operational data with support for horizontal and vertical
/// layouts, responsive behavior, and consistent Material 3 styling. It's designed
/// to show structured information like operational details, status updates, or
/// data summaries.
class DataCard extends StatefulWidget {
  /// The title of the data card
  final String title;

  /// The main content or value to display
  final String content;

  /// Optional subtitle or description
  final String? subtitle;

  /// Optional icon to display
  final IconData? icon;

  /// Optional trailing widget (e.g., status indicator, action button)
  final Widget? trailing;

  /// Layout orientation - horizontal or vertical
  final DataCardLayout layout;

  /// Optional callback when the card is tapped
  final VoidCallback? onTap;

  /// Whether to show hover animation
  final bool animateOnHover;

  /// Custom background color (overrides default surface color)
  final Color? backgroundColor;

  /// Custom border color
  final Color? borderColor;

  /// Whether to show a divider between title and content (vertical layout only)
  final bool showDivider;

  /// Creates a DataCard widget.
  ///
  /// [title] is the header text for the card.
  /// [content] is the main information to display.
  /// [subtitle] is optional additional description text.
  /// [icon] is an optional icon to display.
  /// [trailing] is an optional widget to show at the end.
  /// [layout] determines the arrangement (horizontal or vertical).
  /// [onTap] is called when the card is tapped.
  /// [animateOnHover] determines whether to show hover animations.
  /// [backgroundColor] overrides the default surface color.
  /// [borderColor] sets a custom border color.
  /// [showDivider] adds a divider in vertical layout.
  const DataCard({
    super.key,
    required this.title,
    required this.content,
    this.subtitle,
    this.icon,
    this.trailing,
    this.layout = DataCardLayout.vertical,
    this.onTap,
    this.animateOnHover = true,
    this.backgroundColor,
    this.borderColor,
    this.showDivider = false,
  });

  /// Creates a horizontal layout DataCard.
  factory DataCard.horizontal({
    required String title,
    required String content,
    String? subtitle,
    IconData? icon,
    Widget? trailing,
    VoidCallback? onTap,
    bool animateOnHover = true,
    Color? backgroundColor,
    Color? borderColor,
  }) {
    return DataCard(
      title: title,
      content: content,
      subtitle: subtitle,
      icon: icon,
      trailing: trailing,
      layout: DataCardLayout.horizontal,
      onTap: onTap,
      animateOnHover: animateOnHover,
      backgroundColor: backgroundColor,
      borderColor: borderColor,
    );
  }

  /// Creates a vertical layout DataCard.
  factory DataCard.vertical({
    required String title,
    required String content,
    String? subtitle,
    IconData? icon,
    Widget? trailing,
    VoidCallback? onTap,
    bool animateOnHover = true,
    Color? backgroundColor,
    Color? borderColor,
    bool showDivider = false,
  }) {
    return DataCard(
      title: title,
      content: content,
      subtitle: subtitle,
      icon: icon,
      trailing: trailing,
      layout: DataCardLayout.vertical,
      onTap: onTap,
      animateOnHover: animateOnHover,
      backgroundColor: backgroundColor,
      borderColor: borderColor,
      showDivider: showDivider,
    );
  }

  @override
  State<DataCard> createState() => _DataCardState();
}

class _DataCardState extends State<DataCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: DesignTokens.getAnimationDuration('fast'),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.01).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: DesignTokens.getAnimationCurve('ease-out'),
      ),
    );

    _elevationAnimation = Tween<double>(begin: 2.0, end: 4.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: DesignTokens.getAnimationCurve('ease-out'),
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final screenWidth = MediaQuery.of(context).size.width;
    final deviceType = AppBreakpoints.getDeviceType(screenWidth);

    // Determine responsive layout
    final effectiveLayout = _getEffectiveLayout(deviceType);
    final responsivePadding = _getResponsivePadding(screenWidth);

    return MouseRegion(
      onEnter: (_) {
        if (widget.animateOnHover) {
          _animationController.forward();
        }
      },
      onExit: (_) {
        if (widget.animateOnHover) {
          _animationController.reverse();
        }
      },
      child: GestureDetector(
        onTap: widget.onTap,
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Card(
                elevation: _elevationAnimation.value,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(
                    DesignTokens.getBorderRadius('lg'),
                  ),
                  side: BorderSide(
                    color:
                        widget.borderColor ??
                        colorScheme.outline.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                color: widget.backgroundColor ?? colorScheme.surface,
                child: child,
              ),
            );
          },
          child: Container(
            padding: responsivePadding,
            child:
                effectiveLayout == DataCardLayout.horizontal
                    ? _buildHorizontalLayout(context, colorScheme, screenWidth)
                    : _buildVerticalLayout(context, colorScheme, screenWidth),
          ),
        ),
      ),
    );
  }

  /// Determine effective layout based on device type and user preference
  DataCardLayout _getEffectiveLayout(DeviceType deviceType) {
    // Force vertical layout on mobile for better readability
    if (deviceType == DeviceType.mobile &&
        widget.layout == DataCardLayout.horizontal) {
      return DataCardLayout.vertical;
    }
    return widget.layout;
  }

  /// Get responsive padding based on screen width
  EdgeInsets _getResponsivePadding(double screenWidth) {
    return AppSpacing.getResponsivePadding(
      AppSpacing.cardPaddingInsets,
      screenWidth,
    );
  }

  /// Build horizontal layout
  Widget _buildHorizontalLayout(
    BuildContext context,
    ColorScheme colorScheme,
    double screenWidth,
  ) {
    return Row(
      children: [
        if (widget.icon != null) ...[
          Icon(
            widget.icon,
            color: colorScheme.primary,
            size: DesignTokens.getIconSize('md'),
          ),
          AppSpacing.horizontalSpaceMd,
        ],
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                widget.title,
                style: AppTypography.getResponsiveStyle(
                  AppTypography.textTheme.titleMedium!,
                  screenWidth,
                ).copyWith(
                  color: colorScheme.onSurface,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              if (widget.subtitle != null) ...[
                AppSpacing.verticalSpaceXs,
                Text(
                  widget.subtitle!,
                  style: AppTypography.getResponsiveStyle(
                    AppTypography.textTheme.bodySmall!,
                    screenWidth,
                  ).copyWith(color: colorScheme.onSurfaceVariant),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        ),
        AppSpacing.horizontalSpaceMd,
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              widget.content,
              style: AppTypography.getResponsiveStyle(
                AppTypography.textTheme.titleLarge!,
                screenWidth,
              ).copyWith(
                color: colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.end,
            ),
            if (widget.trailing != null) ...[
              AppSpacing.verticalSpaceXs,
              widget.trailing!,
            ],
          ],
        ),
      ],
    );
  }

  /// Build vertical layout
  Widget _buildVerticalLayout(
    BuildContext context,
    ColorScheme colorScheme,
    double screenWidth,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Row(
                children: [
                  if (widget.icon != null) ...[
                    Icon(
                      widget.icon,
                      color: colorScheme.primary,
                      size: DesignTokens.getIconSize('md'),
                    ),
                    AppSpacing.horizontalSpaceSm,
                  ],
                  Expanded(
                    child: Text(
                      widget.title,
                      style: AppTypography.getResponsiveStyle(
                        AppTypography.textTheme.titleMedium!,
                        screenWidth,
                      ).copyWith(
                        color: colorScheme.onSurface,
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
            if (widget.trailing != null) widget.trailing!,
          ],
        ),
        if (widget.showDivider) ...[
          AppSpacing.verticalSpaceSm,
          Divider(color: colorScheme.outline.withValues(alpha: 0.2), height: 1),
        ],
        AppSpacing.verticalSpaceMd,
        Text(
          widget.content,
          style: AppTypography.getResponsiveStyle(
            AppTypography.textTheme.bodyLarge!,
            screenWidth,
          ).copyWith(color: colorScheme.onSurface, fontWeight: FontWeight.w500),
        ),
        if (widget.subtitle != null) ...[
          AppSpacing.verticalSpaceSm,
          Text(
            widget.subtitle!,
            style: AppTypography.getResponsiveStyle(
              AppTypography.textTheme.bodySmall!,
              screenWidth,
            ).copyWith(color: colorScheme.onSurfaceVariant),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ],
    );
  }
}

/// Layout options for DataCard
enum DataCardLayout {
  /// Horizontal layout - title and content side by side
  horizontal,

  /// Vertical layout - title above content
  vertical,
}
