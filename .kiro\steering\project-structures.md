---
inclusion: always
---

# Project Structure & Organization Guidelines

## Directory Structure Overview

```
aplikasi_sppg/
├── lib/                    # Main application code
│   ├── app/                # App-level configuration and shared components
│   │   ├── config/         # App configuration (router, theme, constants)
│   │   ├── widgets/        # Reusable UI components
│   │   └── utils/          # App-level utilities
│   ├── core/               # Shared business logic and utilities
│   │   ├── errors/         # Custom error types and handling
│   │   ├── services/       # Shared services (Supabase, PowerSync)
│   │   ├── permissions/    # Role-based access control mixins
│   │   └── utils/          # Core utilities and helpers
│   └── features/           # Feature modules (Clean Architecture)
│       └── [feature_name]/
│           ├── data/       # Data layer implementation
│           ├── domain/     # Business logic and contracts
│           └── presentation/ # UI layer (pages, widgets, cubits)
├── assets/                 # Static resources (images, configs)
├── .references/            # Documentation and design references
└── supabase/              # Backend configuration and migrations
```

## Clean Architecture Implementation

Each feature module follows strict layered architecture:

### Data Layer (`features/[feature]/data/`)
- `datasources/` - Remote and local data sources
- `models/` - Data transfer objects with JSON serialization
- `repositories/` - Concrete repository implementations

### Domain Layer (`features/[feature]/domain/`)
- `entities/` - Business objects (extend Equatable)
- `repositories/` - Abstract repository contracts
- `usecases/` - Business logic operations

### Presentation Layer (`features/[feature]/presentation/`)
- `cubit/` - State management (BLoC pattern)
- `pages/` - Screen-level widgets
- `widgets/` - Feature-specific UI components

## File Naming Conventions

- **Files**: `snake_case` for all files and directories
- **Suffixes**: Use descriptive suffixes
  - `_page.dart` - Full screen widgets
  - `_widget.dart` - Reusable UI components
  - `_cubit.dart` - State management classes
  - `_model.dart` - Data transfer objects
  - `_entity.dart` - Domain entities
  - `_repository.dart` - Repository classes
  - `_usecase.dart` - Business logic operations
  - `_datasource.dart` - Data source implementations

## Code Organization Patterns

### Barrel Exports
- Use `index.dart` files for clean imports
- Export public APIs only, keep internal implementation private

### State Management
- One Cubit per feature or major UI component
- Keep business logic in domain layer, UI logic in cubits
- Use BLoC pattern with flutter_bloc 8.1.3

### Permission-Based Features
- Use permission mixins from `core/permissions/`
- Available mixins: `AdminPermission`, `OversightPermission`, `KitchenManagementPermission`, `NutritionPermission`, `FinancialPermission`, `LogisticsPermission`
- Check permissions before rendering UI or executing operations

### Data Models
- All models extend `BaseEntity` and use `@JsonSerializable()`
- Implement `toJson()` and `fromJson()` methods
- Domain entities extend `Equatable`
- Use `toDomain()` method to convert models to entities

### Shared Components Placement
- **App-wide widgets**: `lib/app/widgets/`
- **Feature-specific widgets**: `lib/features/[feature]/presentation/widgets/`
- **Core utilities**: `lib/core/utils/`
- **App configuration**: `lib/app/config/`

### Import Organization
- Flutter/Dart imports first
- Third-party package imports
- Local project imports last
- Use relative imports within the same feature
- Use absolute imports for cross-feature dependencies

## Module Dependencies
- Features should be self-contained with minimal cross-dependencies
- Shared logic goes in `core/` directory
- Cross-feature communication through domain layer contracts
- Use dependency injection for service locator pattern