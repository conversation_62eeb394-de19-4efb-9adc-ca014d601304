import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:aplikasi_sppg/app/design_system/components/navigation/responsive_app_bar.dart';
import 'package:aplikasi_sppg/core/auth/presentation/auth_service.dart';
import 'package:aplikasi_sppg/core/auth/domain/simplified_app_user.dart';
import 'package:aplikasi_sppg/core/auth/domain/simplified_auth_repository.dart';
import 'package:aplikasi_sppg/core/auth/domain/simplified_auth_state.dart';

// Mock classes for testing
class MockAuthService extends Mock implements AuthService {
  @override
  AppUser? get currentUser => null;

  @override
  bool get isAdminYayasan => false;

  @override
  bool get isKepalaDapur => false;
}

class MockAuthRepository extends Mock implements AuthRepository {
  @override
  AppUser? get currentUser => null;

  @override
  AuthState get currentAuthState => const AuthInitialState();

  @override
  Stream<AuthState> get authStateStream =>
      Stream<AuthState>.fromIterable([const AuthInitialState()]);
}

void main() {
  group('ResponsiveAppBar', () {
    testWidgets('renders correctly with title', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(appBar: ResponsiveAppBar(title: 'Test Title')),
        ),
      );

      expect(find.text('Test Title'), findsOneWidget);
    });

    testWidgets('renders actions correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            appBar: ResponsiveAppBar(
              title: 'Test Title',
              actions: [
                IconButton(icon: const Icon(Icons.settings), onPressed: () {}),
              ],
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.settings), findsOneWidget);
    });

    testWidgets('renders navigation items correctly on desktop', (
      WidgetTester tester,
    ) async {
      // Set a desktop screen size
      tester.view.physicalSize = const Size(1280, 800);
      tester.view.devicePixelRatio = 1.0;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            appBar: ResponsiveAppBar(
              title: 'Test Title',
              navigationItems: const [
                ResponsiveAppBarItem(label: 'Home', icon: Icons.home),
                ResponsiveAppBarItem(label: 'Profile', icon: Icons.person),
              ],
            ),
          ),
        ),
      );

      expect(find.text('Home'), findsOneWidget);
      expect(find.text('Profile'), findsOneWidget);
      expect(find.byIcon(Icons.home), findsOneWidget);
      expect(find.byIcon(Icons.person), findsOneWidget);

      // Reset the screen size
      addTearDown(tester.view.reset);
    });

    testWidgets('handles navigation item selection', (
      WidgetTester tester,
    ) async {
      // Set a desktop screen size
      tester.view.physicalSize = const Size(1280, 800);
      tester.view.devicePixelRatio = 1.0;

      int selectedIndex = 0;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            appBar: ResponsiveAppBar(
              title: 'Test Title',
              navigationItems: const [
                ResponsiveAppBarItem(label: 'Home', icon: Icons.home),
                ResponsiveAppBarItem(label: 'Profile', icon: Icons.person),
              ],
              selectedIndex: selectedIndex,
              onNavigationItemSelected: (index) {
                selectedIndex = index;
              },
            ),
          ),
        ),
      );

      await tester.tap(find.text('Profile'));
      await tester.pump();

      expect(selectedIndex, 1);

      // Reset the screen size
      addTearDown(tester.view.reset);
    });

    testWidgets('does not show navigation items on mobile', (
      WidgetTester tester,
    ) async {
      // Set a mobile screen size
      tester.view.physicalSize = const Size(360, 640);
      tester.view.devicePixelRatio = 1.0;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            appBar: ResponsiveAppBar(
              title: 'Test Title',
              navigationItems: const [
                ResponsiveAppBarItem(label: 'Home', icon: Icons.home),
                ResponsiveAppBarItem(label: 'Profile', icon: Icons.person),
              ],
            ),
          ),
        ),
      );

      // Navigation items should not be visible on mobile
      expect(find.text('Home'), findsNothing);
      expect(find.text('Profile'), findsNothing);

      // Reset the screen size
      addTearDown(tester.view.reset);
    });

    // More tests would be added for permission-based rendering
    // but would require mocking the auth system
  });
}
