import 'package:fluent_ui/fluent_ui.dart';
import 'package:logger/logger.dart';
import '../constants/app_colors.dart';
import '../constants/app_typography.dart';
import '../constants/app_spacing.dart';
import '../constants/app_radius.dart';

/// Card Factory for SOD-MBG with Fluent UI
/// Provides consistent card styling with Fluent UI design principles
class AppCardFactory {
  static final Logger _logger = Logger();

  // Private constructor
  AppCardFactory._();

  // ===== BASIC CARDS =====
  
  /// Basic card with content
  static Widget basic({
    required Widget child,
    EdgeInsets? padding,
    Color? backgroundColor,
    double? elevation,
    BorderRadius? borderRadius,
    Border? border,
  }) {
    _logger.d('Creating basic Fluent UI card');
    
    return Card(
      padding: padding ?? const EdgeInsets.all(AppSpacing.md),
      backgroundColor: backgroundColor ?? AppColors.surfaceColor,
      borderRadius: borderRadius ?? BorderRadius.circular(AppRadius.card),
      child: child,
    );
  }

  /// Outlined card variant
  static Widget outlined({
    required Widget child,
    EdgeInsets? padding,
    Color? borderColor,
    double? borderWidth,
    BorderRadius? borderRadius,
    Color? backgroundColor,
  }) {
    _logger.d('Creating outlined Fluent UI card');
    
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.transparent,
        borderRadius: borderRadius ?? BorderRadius.circular(AppRadius.card),
        border: Border.all(
          color: borderColor ?? AppColors.borderPrimary,
          width: borderWidth ?? 1.0,
        ),
      ),
      child: Padding(
        padding: padding ?? const EdgeInsets.all(AppSpacing.md),
        child: child,
      ),
    );
  }

  /// Elevated card variant
  static Widget elevated({
    required Widget child,
    EdgeInsets? padding,
    Color? backgroundColor,
    double? elevation,
    BorderRadius? borderRadius,
  }) {
    _logger.d('Creating elevated Fluent UI card');
    
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.surfaceColor,
        borderRadius: borderRadius ?? BorderRadius.circular(AppRadius.card),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: elevation ?? 4.0,
            offset: Offset(0, (elevation ?? 2.0)),
          ),
        ],
      ),
      child: Padding(
        padding: padding ?? const EdgeInsets.all(AppSpacing.md),
        child: child,
      ),
    );
  }

  /// Header card with title and optional subtitle
  static Widget header({
    required String title,
    String? subtitle,
    Widget? child,
    Widget? trailing,
    EdgeInsets? padding,
    Color? backgroundColor,
    VoidCallback? onTap,
  }) {
    _logger.d('Creating header Fluent UI card: $title');
    
    return Card(
      padding: EdgeInsets.zero,
      backgroundColor: backgroundColor ?? AppColors.surfaceColor,
      borderRadius: BorderRadius.circular(AppRadius.card),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header section
          Container(
            padding: padding ?? const EdgeInsets.all(AppSpacing.md),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.05),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(AppRadius.card),
                topRight: Radius.circular(AppRadius.card),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: AppTypography.h3,
                      ),
                      if (subtitle != null) ...[
                        const SizedBox(height: AppSpacing.xs),
                        Text(
                          subtitle,
                          style: AppTypography.bodyMedium.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                if (trailing != null) trailing,
              ],
            ),
          ),
          
          // Content section
          if (child != null)
            Padding(
              padding: const EdgeInsets.all(AppSpacing.md),
              child: child,
            ),
        ],
      ),
    );
  }

  /// Stats card for displaying metrics
  static Widget stats({
    required String title,
    required String value,
    String? subtitle,
    Widget? icon,
    Color? iconColor,
    Color? valueColor,
    String? trend,
    bool isPositiveTrend = true,
    EdgeInsets? padding,
    VoidCallback? onTap,
  }) {
    _logger.d('Creating stats Fluent UI card: $title');
    
    return Card(
      padding: padding ?? const EdgeInsets.all(AppSpacing.md),
      backgroundColor: AppColors.surfaceColor,
      borderRadius: BorderRadius.circular(AppRadius.card),
      child: GestureDetector(
        onTap: onTap,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with icon and title
            Row(
              children: [
                if (icon != null) ...[
                  Container(
                    padding: const EdgeInsets.all(AppSpacing.sm),
                    decoration: BoxDecoration(
                      color: (iconColor ?? AppColors.primary).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(AppRadius.button),
                    ),
                    child: icon,
                  ),
                  const SizedBox(width: AppSpacing.sm),
                ],
                Expanded(
                  child: Text(
                    title,
                    style: AppTypography.bodyMedium.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppSpacing.sm),
            
            // Value
            Text(
              value,
              style: AppTypography.h1.copyWith(
                color: valueColor ?? AppColors.textPrimary,
              ),
            ),
            
            // Subtitle and trend
            if (subtitle != null || trend != null) ...[
              const SizedBox(height: AppSpacing.xs),
              Row(
                children: [
                  if (subtitle != null)
                    Expanded(
                      child: Text(
                        subtitle,
                        style: AppTypography.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ),
                  if (trend != null)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppSpacing.xs,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: isPositiveTrend 
                            ? AppColors.successGreen.withValues(alpha: 0.1)
                            : AppColors.dangerRed.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(AppRadius.chip),
                      ),
                      child: Text(
                        trend,
                        style: AppTypography.bodySmall.copyWith(
                          color: isPositiveTrend 
                              ? AppColors.successGreen
                              : AppColors.dangerRed,
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Action card with primary action button
  static Widget action({
    required String title,
    required String description,
    required String actionText,
    required VoidCallback onAction,
    Widget? icon,
    Color? iconColor,
    String? secondaryActionText,
    VoidCallback? onSecondaryAction,
    EdgeInsets? padding,
  }) {
    _logger.d('Creating action Fluent UI card: $title');
    
    return Card(
      padding: padding ?? const EdgeInsets.all(AppSpacing.md),
      backgroundColor: AppColors.surfaceColor,
      borderRadius: BorderRadius.circular(AppRadius.card),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon and title
          Row(
            children: [
              if (icon != null) ...[
                Container(
                  padding: const EdgeInsets.all(AppSpacing.sm),
                  decoration: BoxDecoration(
                    color: (iconColor ?? AppColors.primary).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppRadius.button),
                  ),
                  child: icon,
                ),
                const SizedBox(width: AppSpacing.sm),
              ],
              Expanded(
                child: Text(
                  title,
                  style: AppTypography.h3,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppSpacing.sm),
          
          // Description
          Text(
            description,
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          
          const SizedBox(height: AppSpacing.md),
          
          // Actions
          Row(
            children: [
              Button(
                onPressed: onAction,
                style: ButtonStyle(
                  backgroundColor: WidgetStateProperty.all(AppColors.primary),
                  foregroundColor: WidgetStateProperty.all(AppColors.textOnPrimary),
                  padding: WidgetStateProperty.all(
                    const EdgeInsets.symmetric(
                      horizontal: AppSpacing.md,
                      vertical: AppSpacing.sm,
                    ),
                  ),
                ),
                child: Text(actionText),
              ),
              
              if (secondaryActionText != null && onSecondaryAction != null) ...[
                const SizedBox(width: AppSpacing.sm),
                Button(
                  onPressed: onSecondaryAction,
                  style: ButtonStyle(
                    backgroundColor: WidgetStateProperty.all(Colors.transparent),
                    foregroundColor: WidgetStateProperty.all(AppColors.primary),
                    padding: WidgetStateProperty.all(
                      const EdgeInsets.symmetric(
                        horizontal: AppSpacing.md,
                        vertical: AppSpacing.sm,
                      ),
                    ),
                  ),
                  child: Text(secondaryActionText),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  /// List card for displaying items
  static Widget list({
    required List<Widget> children,
    String? title,
    Widget? trailing,
    EdgeInsets? padding,
    Color? backgroundColor,
    bool showDividers = true,
  }) {
    _logger.d('Creating list Fluent UI card with ${children.length} items');
    
    return Card(
      padding: EdgeInsets.zero,
      backgroundColor: backgroundColor ?? AppColors.surfaceColor,
      borderRadius: BorderRadius.circular(AppRadius.card),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header if provided
          if (title != null)
            Container(
              padding: padding ?? const EdgeInsets.all(AppSpacing.md),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.05),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(AppRadius.card),
                  topRight: Radius.circular(AppRadius.card),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      title,
                      style: AppTypography.h3,
                    ),
                  ),
                  if (trailing != null) trailing,
                ],
              ),
            ),
          
          // List items
          ...children.asMap().entries.map((entry) {
            final index = entry.key;
            final child = entry.value;
            final isLast = index == children.length - 1;
            
            return Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppSpacing.md,
                vertical: AppSpacing.sm,
              ),
              decoration: BoxDecoration(
                border: showDividers && !isLast
                    ? Border(
                        bottom: BorderSide(
                          color: AppColors.neutralGray200,
                          width: 1,
                        ),
                      )
                    : null,
              ),
              child: child,
            );
          }),
        ],
      ),
    );
  }

  /// Info card for displaying information with status
  static Widget info({
    required String title,
    required String message,
    InfoCardType type = InfoCardType.info,
    Widget? icon,
    bool isDismissible = false,
    VoidCallback? onDismiss,
    EdgeInsets? padding,
  }) {
    _logger.d('Creating info Fluent UI card: $title');
    
    final typeConfig = _getInfoCardConfig(type);
    
    return Card(
      padding: padding ?? const EdgeInsets.all(AppSpacing.md),
      backgroundColor: typeConfig.backgroundColor,
      borderRadius: BorderRadius.circular(AppRadius.card),
      child: Row(
        children: [
          // Icon
          Container(
            padding: const EdgeInsets.all(AppSpacing.sm),
            decoration: BoxDecoration(
              color: typeConfig.iconColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(AppRadius.button),
            ),
            child: Icon(
              (icon as Icon?)?.icon ?? typeConfig.icon,
              color: typeConfig.iconColor,
              size: 20,
            ),
          ),
          
          const SizedBox(width: AppSpacing.sm),
          
          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTypography.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: typeConfig.textColor,
                  ),
                ),
                const SizedBox(height: AppSpacing.xs),
                Text(
                  message,
                  style: AppTypography.bodySmall.copyWith(
                    color: typeConfig.textColor,
                  ),
                ),
              ],
            ),
          ),
          
          // Dismiss button
          if (isDismissible && onDismiss != null)
            IconButton(
              onPressed: onDismiss,
              icon: const Icon(FluentIcons.chrome_close, size: 16),
              style: ButtonStyle(
                backgroundColor: WidgetStateProperty.all(Colors.transparent),
                foregroundColor: WidgetStateProperty.all(typeConfig.textColor),
                padding: WidgetStateProperty.all(const EdgeInsets.all(4)),
              ),
            ),
        ],
      ),
    );
  }

  /// Kitchen-specific card for operational displays
  static Widget kitchen({
    required String title,
    required Widget child,
    String? subtitle,
    Widget? headerAction,
    Color? statusColor,
    String? statusText,
    EdgeInsets? padding,
    bool isHighVisibility = false,
  }) {
    _logger.d('Creating kitchen Fluent UI card: $title');
    
    return Card(
      padding: EdgeInsets.zero,
      backgroundColor: AppColors.surfaceColor,
      borderRadius: BorderRadius.circular(AppRadius.card),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with status indicator
          Container(
            padding: padding ?? const EdgeInsets.all(AppSpacing.md),
            decoration: BoxDecoration(
              color: statusColor?.withValues(alpha: 0.1) ?? AppColors.primary.withValues(alpha: 0.05),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(AppRadius.card),
                topRight: Radius.circular(AppRadius.card),
              ),
            ),
            child: Row(
              children: [
                // Status indicator
                if (statusColor != null)
                  Container(
                    width: 4,
                    height: 24,
                    decoration: BoxDecoration(
                      color: statusColor,
                      borderRadius: BorderRadius.circular(AppRadius.chip),
                    ),
                  ),
                if (statusColor != null) const SizedBox(width: AppSpacing.sm),
                
                // Title and subtitle
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: isHighVisibility 
                            ? AppTypography.kitchenDisplay 
                            : AppTypography.h3,
                      ),
                      if (subtitle != null) ...[
                        const SizedBox(height: AppSpacing.xs),
                        Text(
                          subtitle,
                          style: AppTypography.bodyMedium.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                
                // Status text
                if (statusText != null)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppSpacing.sm,
                      vertical: AppSpacing.xs,
                    ),
                    decoration: BoxDecoration(
                      color: statusColor ?? AppColors.primary,
                      borderRadius: BorderRadius.circular(AppRadius.chip),
                    ),
                    child: Text(
                      statusText,
                      style: AppTypography.bodySmall.copyWith(
                        color: AppColors.textOnPrimary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                
                // Header action
                if (headerAction != null) ...[
                  const SizedBox(width: AppSpacing.sm),
                  headerAction,
                ],
              ],
            ),
          ),
          
          // Content
          Padding(
            padding: const EdgeInsets.all(AppSpacing.md),
            child: child,
          ),
        ],
      ),
    );
  }

  // ===== METRIC CARD =====
  
  /// Metric card for displaying statistics and KPIs
  static Widget metric({
    required String title,
    required String value,
    String? subtitle,
    IconData? icon,
    Color? iconColor,
    Color? backgroundColor,
    EdgeInsets? padding,
    VoidCallback? onTap,
  }) {
    _logger.d('Creating metric Fluent UI card: $title');
    
    return Card(
      padding: EdgeInsets.zero,
      backgroundColor: backgroundColor ?? AppColors.surfaceColor,
      borderRadius: BorderRadius.circular(AppRadius.card),
      child: GestureDetector(
        onTap: onTap,
        child: Padding(
          padding: padding ?? const EdgeInsets.all(AppSpacing.md),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with icon and title
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      title,
                      style: AppTypography.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ),
                  if (icon != null)
                    Icon(
                      icon,
                      size: 20,
                      color: iconColor ?? AppColors.primary,
                    ),
                ],
              ),
              
              const SizedBox(height: AppSpacing.sm),
              
              // Value
              Text(
                value,
                style: AppTypography.h2.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              
              // Subtitle
              if (subtitle != null) ...[
                const SizedBox(height: AppSpacing.xs),
                Text(
                  subtitle,
                  style: AppTypography.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  // ===== HELPER METHODS =====
  
  /// Get info card configuration based on type
  static _InfoCardConfig _getInfoCardConfig(InfoCardType type) {
    switch (type) {
      case InfoCardType.info:
        return _InfoCardConfig(
          backgroundColor: AppColors.infoBlue.withValues(alpha: 0.1),
          iconColor: AppColors.infoBlue,
          textColor: AppColors.textPrimary,
          icon: FluentIcons.info,
        );
      case InfoCardType.success:
        return _InfoCardConfig(
          backgroundColor: AppColors.successGreen.withValues(alpha: 0.1),
          iconColor: AppColors.successGreen,
          textColor: AppColors.textPrimary,
          icon: FluentIcons.check_mark,
        );
      case InfoCardType.warning:
        return _InfoCardConfig(
          backgroundColor: AppColors.warningOrange.withValues(alpha: 0.1),
          iconColor: AppColors.warningOrange,
          textColor: AppColors.textPrimary,
          icon: FluentIcons.warning,
        );
      case InfoCardType.error:
        return _InfoCardConfig(
          backgroundColor: AppColors.dangerRed.withValues(alpha: 0.1),
          iconColor: AppColors.dangerRed,
          textColor: AppColors.textPrimary,
          icon: FluentIcons.error,
        );
    }
  }
}

/// Info card type enumeration
enum InfoCardType {
  info,
  success,
  warning,
  error,
}

/// Info card configuration
class _InfoCardConfig {
  final Color backgroundColor;
  final Color iconColor;
  final Color textColor;
  final IconData icon;

  const _InfoCardConfig({
    required this.backgroundColor,
    required this.iconColor,
    required this.textColor,
    required this.icon,
  });
}

/// Card Extensions
extension AppCardExtensions on Card {
  /// Add tap gesture to card
  Widget withTap(VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: this,
    );
  }

  /// Add shadow to card
  Widget withShadow({
    double elevation = 2,
    Color shadowColor = const Color(0x1F000000),
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppRadius.card),
        boxShadow: [
          BoxShadow(
            color: shadowColor,
            blurRadius: elevation * 2,
            offset: Offset(0, elevation),
          ),
        ],
      ),
      child: this,
    );
  }
}
