{"enabled": true, "name": "Flutter Documentation Sync", "description": "Monitors Flutter source files and configuration files for changes, then updates project documentation in README.md and .references folder to keep documentation in sync with code changes", "version": "1", "when": {"type": "fileEdited", "patterns": ["lib/**/*.dart", "pubspec.yaml", "analysis_options.yaml", "scripts/*.dart", ".env.example"]}, "then": {"type": "askAgent", "prompt": "Source files in this Flutter project have been modified. Please review the changes and update the documentation accordingly. Focus on:\n\n1. Update README.md if there are significant architectural changes, new features, or setup instructions that need modification\n2. Update relevant documentation in the .references folder if there are changes to:\n   - Navigation structure or admin functionality\n   - Kitchen module design or workflow\n   - Overall system architecture\n\nConsider the following when updating documentation:\n- This is a Flutter app for kitchen operations management (SOD-MBG system)\n- The app uses Clean Architecture with BLoC pattern\n- Key technologies: Flutter, Supabase, PowerSync, Fluent UI\n- Target users include kitchen staff, nutritionists, accountants, and foundation administrators\n- The system supports offline-first operations\n\nPlease analyze the code changes and determine what documentation updates are needed to keep everything current and accurate."}}