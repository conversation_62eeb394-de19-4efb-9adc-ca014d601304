import 'package:flutter/material.dart';
import '../auth/presentation/auth_service.dart';

/// Base permission mixin for role-based access control
mixin BasePermission {
  /// Permission identifier used for checking access
  String get permissionId;

  /// Permission display name for UI
  String get displayName;

  /// Permission description
  String get description;
}

/// Admin Yayasan permission mixin
/// Grants full system access with administrative capabilities
mixin AdminPermission implements BasePermission {
  @override
  String get permissionId => 'admin';

  @override
  String get displayName => 'Admin Yayasan';

  @override
  String get description =>
      'Akses penuh ke seluruh sistem dengan kemampuan administratif';
}

/// Perwakilan Yayasan permission mixin
/// Grants read access to all data with verification capabilities
mixin OversightPermission implements BasePermission {
  @override
  String get permissionId => 'oversight';

  @override
  String get displayName => 'Perwakilan Yayasan';

  @override
  String get description =>
      'Akses baca ke semua data dengan kemampuan verifikasi';
}

/// Kepala Dapur SPPG permission mixin
/// Grants full access to kitchen operations for assigned SPPG only
mixin KitchenManagementPermission implements BasePermission {
  @override
  String get permissionId => 'kitchen_management';

  @override
  String get displayName => 'Kepala Dapur SPPG';

  @override
  String get description =>
      'Akses penuh ke operasi dapur untuk SPPG yang ditugaskan';
}

/// Ahli Gizi permission mixin
/// Grants menu and nutrition management capabilities
mixin NutritionPermission implements BasePermission {
  @override
  String get permissionId => 'nutrition';

  @override
  String get displayName => 'Ahli Gizi';

  @override
  String get description => 'Kemampuan manajemen menu dan nutrisi';
}

/// Akuntan permission mixin
/// Grants financial transaction capabilities
mixin FinancialPermission implements BasePermission {
  @override
  String get permissionId => 'financial';

  @override
  String get displayName => 'Akuntan';

  @override
  String get description => 'Kemampuan transaksi keuangan';
}

/// Pengawas Pemeliharaan & Penghantaran permission mixin
/// Grants logistics and maintenance capabilities
mixin LogisticsPermission implements BasePermission {
  @override
  String get permissionId => 'logistics';

  @override
  String get displayName => 'Pengawas Pemeliharaan & Penghantaran';

  @override
  String get description => 'Kemampuan logistik dan pemeliharaan';
}

/// Extension on BuildContext for permission checking
extension PermissionContextExtension on BuildContext {
  /// Check if current user has specific permission
  bool hasPermission<T extends BasePermission>() {
    // Get current user from AuthService
    final authService = AuthService.instance;
    final currentUser = authService.currentUser;

    // Get permission ID from the mixin type
    final permissionType = T;
    String permissionId = '';

    if (permissionType == AdminPermission) {
      permissionId = 'admin';
    } else if (permissionType == OversightPermission) {
      permissionId = 'oversight';
    } else if (permissionType == KitchenManagementPermission) {
      permissionId = 'kitchen_management';
    } else if (permissionType == NutritionPermission) {
      permissionId = 'nutrition';
    } else if (permissionType == FinancialPermission) {
      permissionId = 'financial';
    } else if (permissionType == LogisticsPermission) {
      permissionId = 'logistics';
    }

    // Check if user has permission based on role
    if (currentUser == null) return false;

    switch (permissionId) {
      case 'admin':
        return currentUser.isAdminYayasan;
      case 'oversight':
        return currentUser.isPerwakilanYayasan;
      case 'kitchen_management':
        return currentUser.isKepalaDapur;
      case 'nutrition':
        return currentUser.isAhliGizi;
      case 'financial':
        return currentUser.isAkuntan;
      case 'logistics':
        return currentUser.isPengawasPemeliharaan;
      default:
        return currentUser.hasAccessTo(permissionId);
    }
  }

  /// Check if current user has a specific role
  bool hasRole(String role) {
    final authService = AuthService.instance;
    final currentUser = authService.currentUser;
    return currentUser?.role == role;
  }

  /// Check if current user is Admin Yayasan
  bool get isAdminYayasan => AuthService.instance.isAdminYayasan;

  /// Check if current user is Kepala Dapur
  bool get isKepalaDapur => AuthService.instance.isKepalaDapur;

  /// Check if current user is Ahli Gizi
  bool get isAhliGizi => AuthService.instance.isAhliGizi;

  /// Check if current user is Akuntan
  bool get isAkuntan => AuthService.instance.isAkuntan;

  /// Check if current user is Pengawas Pemeliharaan
  bool get isPengawasPemeliharaan =>
      AuthService.instance.isPengawasPemeliharaan;
}

/// Widget that only shows content if user has required permission
class PermissionAwareWidget<T extends BasePermission> extends StatelessWidget {
  final Widget child;
  final Widget? fallback;

  const PermissionAwareWidget({super.key, required this.child, this.fallback});

  @override
  Widget build(BuildContext context) {
    if (context.hasPermission<T>()) {
      return child;
    }
    return fallback ?? const SizedBox.shrink();
  }
}
