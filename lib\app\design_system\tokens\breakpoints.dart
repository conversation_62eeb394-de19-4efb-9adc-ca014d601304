import 'package:flutter/material.dart';

/// Responsive breakpoints for SOD-MBG application
/// Defines standard screen size breakpoints for responsive design
class AppBreakpoints {
  AppBreakpoints._();

  /// Breakpoint values (in logical pixels)
  static const double mobile = 600;
  static const double tablet = 1024;
  static const double desktop = 1440;

  /// Device type detection methods
  static bool isMobile(double width) => width < mobile;
  static bool isTablet(double width) => width >= mobile && width < desktop;
  static bool isDesktop(double width) => width >= desktop;

  /// Get device type as enum
  static DeviceType getDeviceType(double width) {
    if (isMobile(width)) return DeviceType.mobile;
    if (isTablet(width)) return DeviceType.tablet;
    return DeviceType.desktop;
  }

  /// Get device type from BuildContext
  static DeviceType getDeviceTypeFromContext(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return getDeviceType(width);
  }

  /// Get responsive value based on screen width
  /// Allows different values for mobile, tablet, and desktop
  static T getResponsiveValue<T>({
    required double width,
    required T mobile,
    required T tablet,
    required T desktop,
  }) {
    if (isMobile(width)) return mobile;
    if (isTablet(width)) return tablet;
    return desktop;
  }

  /// Get responsive value from BuildContext
  static T getResponsiveValueFromContext<T>({
    required BuildContext context,
    required T mobile,
    required T tablet,
    required T desktop,
  }) {
    final width = MediaQuery.of(context).size.width;
    return getResponsiveValue(
      width: width,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  /// Get column count for grid layouts based on screen width
  static int getGridColumnCount(double width) {
    if (width < mobile) return 1;
    if (width < tablet) return 2;
    if (width < desktop) return 3;
    return 4;
  }

  /// Get responsive padding based on screen width
  static EdgeInsets getResponsivePadding(double width) {
    if (width < mobile) {
      return const EdgeInsets.all(16.0); // Mobile padding
    } else if (width < desktop) {
      return const EdgeInsets.all(24.0); // Tablet padding
    } else {
      return const EdgeInsets.all(32.0); // Desktop padding
    }
  }

  /// Get responsive margin based on screen width
  static EdgeInsets getResponsiveMargin(double width) {
    if (width < mobile) {
      return const EdgeInsets.all(8.0); // Mobile margin
    } else if (width < desktop) {
      return const EdgeInsets.all(16.0); // Tablet margin
    } else {
      return const EdgeInsets.all(24.0); // Desktop margin
    }
  }

  /// Get responsive spacing multiplier based on screen width
  static double getResponsiveSpacingMultiplier(double width) {
    if (width < mobile) return 0.75; // Mobile - 25% smaller
    if (width < desktop) return 1.0; // Tablet - normal
    return 1.25; // Desktop - 25% larger
  }
}

/// Device type enumeration
enum DeviceType { mobile, tablet, desktop }
