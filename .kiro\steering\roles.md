---
inclusion: always
---

# Role-Based Access Control System

## Overview
SOD-MBG implements a comprehensive role-based access control system for managing kitchen operations across multiple SPPG (Nutrition Service Units). Each user role has specific permissions and responsibilities within the system.

## User Roles & Permissions

### 1. <PERSON><PERSON> (Foundation Administrator)
- **Permission Mixin**: `AdminPermission`
- **Access Level**: Full system access with administrative capabilities
- **Responsibilities**:
  - Manage all SPPG units and their operations
  - Configure system settings and user permissions
  - Access all financial and operational reports
  - Manage user accounts and role assignments
- **Implementation**: Use `AdminPermission` mixin for all administrative features

### 2. <PERSON><PERSON><PERSON><PERSON> (Foundation Representative)
- **Permission Mixin**: `OversightPermission`
- **Access Level**: Read access to all data with verification capabilities
- **Responsibilities**:
  - Monitor operations across all SPPG units
  - Verify and approve critical operations
  - Generate oversight reports
  - Quality assurance and compliance monitoring
- **Implementation**: Use `OversightPermission` mixin for monitoring and verification features

### 3. Kepala Dapur SPPG (SPPG Kitchen Head)
- **Permission Mixin**: `KitchenManagementPermission`
- **Access Level**: Full access to kitchen operations for assigned SPPG only
- **Responsibilities**:
  - Manage daily kitchen operations for their SPPG
  - Supervise kitchen staff and meal preparation
  - Handle inventory and supply management
  - Generate operational reports for their unit
- **Implementation**: Use `KitchenManagementPermission` mixin with SPPG-specific filtering

### 4. Ahli Gizi (Nutritionist)
- **Permission Mixin**: `NutritionPermission`
- **Access Level**: Menu and nutrition management capabilities
- **Responsibilities**:
  - Design and approve meal menus
  - Ensure nutritional compliance with national standards
  - Monitor nutritional quality across SPPG units
  - Generate nutrition reports and recommendations
- **Implementation**: Use `NutritionPermission` mixin for menu and nutrition features

### 5. Akuntan (Accountant)
- **Permission Mixin**: `FinancialPermission`
- **Access Level**: Financial transaction capabilities
- **Responsibilities**:
  - Manage financial transactions and budgets
  - Process payments and financial approvals
  - Generate financial reports and audits
  - Monitor cost efficiency across operations
- **Implementation**: Use `FinancialPermission` mixin for financial features

### 6. Pengawas Pemeliharaan & Penghantaran (Maintenance & Delivery Supervisor)
- **Permission Mixin**: `LogisticsPermission`
- **Access Level**: Logistics and maintenance capabilities
- **Responsibilities**:
  - Manage equipment maintenance schedules
  - Oversee meal delivery operations
  - Handle supply chain and logistics coordination
  - Monitor equipment status and maintenance records
- **Implementation**: Use `LogisticsPermission` mixin for logistics features

## Implementation Guidelines

### Permission Checking
```dart
// Check permissions before rendering UI
if (context.hasPermission<AdminPermission>()) {
  // Render admin-only features
}

// Check permissions before executing operations
@override
Widget build(BuildContext context) {
  return PermissionGuard<KitchenManagementPermission>(
    child: KitchenOperationsPage(),
    fallback: UnauthorizedWidget(),
  );
}
```

### SPPG-Specific Access
- Kitchen Head roles are restricted to their assigned SPPG unit
- Always filter data by `sppg_id` for role-based access
- Implement SPPG assignment validation in authentication flow

### Role Hierarchy
1. **Admin Yayasan** - Highest level, can access all features
2. **Perwakilan Yayasan** - Oversight level, read-only access to all data
3. **Specialized Roles** - Domain-specific access (Kitchen, Nutrition, Finance, Logistics)

## Security Considerations

### Authentication & Authorization
- Verify user role on both client and server side
- Implement role-based route guards using go_router
- Store role information securely in user session
- Validate permissions before API calls

### Data Access Patterns
- Always apply role-based filtering at the data layer
- Use Supabase Row Level Security (RLS) policies
- Implement audit logging for sensitive operations
- Ensure offline data sync respects role permissions

### UI/UX Guidelines
- Hide features that users don't have permission to access
- Provide clear feedback when access is denied
- Use progressive disclosure based on user role
- Implement graceful degradation for unauthorized actions

## Best Practices

1. **Fail Secure**: Default to denying access when permissions are unclear
2. **Principle of Least Privilege**: Grant minimum necessary permissions
3. **Role Validation**: Always validate roles on both client and server
4. **Audit Trail**: Log all role-based access attempts
5. **Testing**: Include role-based access tests in your test suite