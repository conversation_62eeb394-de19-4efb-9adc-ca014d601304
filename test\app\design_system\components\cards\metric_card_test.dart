import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:aplikasi_sppg/app/design_system/components/cards/metric_card.dart';

void main() {
  group('MetricCard', () {
    testWidgets('renders correctly with required props', (
      WidgetTester tester,
    ) async {
      // Arrange
      const title = 'Total Meals';
      const value = '1,234';

      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(body: MetricCard(title: title, value: value)),
        ),
      );

      // Assert
      expect(find.text(title), findsOneWidget);
      expect(find.text(value), findsOneWidget);
    });

    testWidgets('renders with icon when provided', (WidgetTester tester) async {
      // Arrange
      const title = 'Total Meals';
      const value = '1,234';
      const icon = Icons.restaurant;

      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: MetricCard(title: title, value: value, icon: icon),
          ),
        ),
      );

      // Assert
      expect(find.byIcon(icon), findsOneWidget);
    });

    testWidgets('renders subtitle when provided', (WidgetTester tester) async {
      // Arrange
      const title = 'Total Meals';
      const value = '1,234';
      const subtitle = 'Last 7 days';

      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: MetricCard(title: title, value: value, subtitle: subtitle),
          ),
        ),
      );

      // Assert
      expect(find.text(subtitle), findsOneWidget);
    });

    testWidgets('calls onTap callback when tapped', (
      WidgetTester tester,
    ) async {
      // Arrange
      const title = 'Total Meals';
      const value = '1,234';
      bool wasTapped = false;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MetricCard(
              title: title,
              value: value,
              onTap: () {
                wasTapped = true;
              },
            ),
          ),
        ),
      );

      await tester.tap(find.byType(MetricCard));
      await tester.pump();

      // Assert
      expect(wasTapped, isTrue);
    });

    group('variants', () {
      testWidgets('success variant has correct color', (
        WidgetTester tester,
      ) async {
        // Act
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MetricCard.success(title: 'Success Metric', value: '100%'),
            ),
          ),
        );

        // Assert
        final container = tester.widget<Container>(
          find.descendant(
            of: find.byType(MetricCard),
            matching: find.byType(Container).last,
          ),
        );

        expect(container, isNotNull);
      });

      testWidgets('warning variant has correct color', (
        WidgetTester tester,
      ) async {
        // Act
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MetricCard.warning(title: 'Warning Metric', value: '75%'),
            ),
          ),
        );

        // Assert
        final container = tester.widget<Container>(
          find.descendant(
            of: find.byType(MetricCard),
            matching: find.byType(Container).last,
          ),
        );

        expect(container, isNotNull);
      });

      testWidgets('error variant has correct color', (
        WidgetTester tester,
      ) async {
        // Act
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MetricCard.error(title: 'Error Metric', value: '25%'),
            ),
          ),
        );

        // Assert
        final container = tester.widget<Container>(
          find.descendant(
            of: find.byType(MetricCard),
            matching: find.byType(Container).last,
          ),
        );

        expect(container, isNotNull);
      });

      testWidgets('info variant has correct color', (
        WidgetTester tester,
      ) async {
        // Act
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: MetricCard.info(title: 'Info Metric', value: '50%'),
            ),
          ),
        );

        // Assert
        final container = tester.widget<Container>(
          find.descendant(
            of: find.byType(MetricCard),
            matching: find.byType(Container).last,
          ),
        );

        expect(container, isNotNull);
      });
    });
  });
}
