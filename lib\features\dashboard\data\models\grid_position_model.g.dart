// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'grid_position_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GridPositionModel _$GridPositionModelFromJson(Map<String, dynamic> json) =>
    GridPositionModel(
      column: (json['column'] as num).toInt(),
      row: (json['row'] as num).toInt(),
      columnSpan: (json['column_span'] as num?)?.toInt() ?? 1,
      rowSpan: (json['row_span'] as num?)?.toInt() ?? 1,
      minHeight: (json['min_height'] as num?)?.toDouble(),
      maxHeight: (json['max_height'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$GridPositionModelToJson(GridPositionModel instance) =>
    <String, dynamic>{
      'column': instance.column,
      'row': instance.row,
      'column_span': instance.columnSpan,
      'row_span': instance.rowSpan,
      'min_height': instance.minHeight,
      'max_height': instance.maxHeight,
    };
