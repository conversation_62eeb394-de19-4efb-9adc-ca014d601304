/// Navigation components for the SOD-MBG application
///
/// This library provides navigation components including:
/// - AppSidebar: A responsive sidebar navigation with role-based permissions
/// - AppSidebarScaffold: A scaffold that integrates sidebar with main content
/// - AppSidebarItem: Configuration for sidebar navigation items
/// - AppBottomNavigation: A bottom navigation bar for mobile layouts
/// - AppBottomNavigationScaffold: A scaffold with integrated bottom navigation
/// - ResponsiveAppBar: A responsive app bar with navigation items
/// - NavigationItems: Centralized navigation configuration
library;

export 'app_sidebar.dart';
export 'app_sidebar_example.dart';
export 'app_bottom_navigation.dart';
export 'app_bottom_navigation_example.dart';
export 'responsive_app_bar.dart';
export 'navigation_items.dart';
