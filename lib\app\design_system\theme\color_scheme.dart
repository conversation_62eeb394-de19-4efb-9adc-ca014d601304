import 'package:flutter/material.dart';

/// Application color scheme following Material Design 3 guidelines
/// with custom colors for SOD-MBG kitchen operations theme
class AppColorScheme {
  AppColorScheme._();

  // Primary Colors - Green for kitchen/food identity
  static const Color primaryGreen = Color(0xFF2E7D32);
  static const Color primaryGreenLight = Color(0xFF60AD5E);
  static const Color primaryGreenDark = Color(0xFF005005);

  // Secondary Colors - Blue for actions and information
  static const Color secondaryBlue = Color(0xFF1976D2);
  static const Color secondaryBlueLight = Color(0xFF63A4FF);
  static const Color secondaryBlueDark = Color(0xFF004BA0);

  // Surface Colors - Neutral for backgrounds
  static const Color surfaceLight = Color(0xFFFAFAFA);
  static const Color surfaceDark = Color(0xFF121212);
  static const Color surfaceVariant = Color(0xFFF5F5F5);
  static const Color surfaceVariantDark = Color(0xFF2C2C2C);

  // Status Colors
  static const Color successGreen = Color(0xFF4CAF50);
  static const Color warningOrange = Color(0xFFFF9800);
  static const Color errorRed = Color(0xFFF44336);
  static const Color infoBlue = Color(0xFF2196F3);

  // Neutral Colors
  static const Color neutralGrey = Color(0xFF9E9E9E);
  static const Color neutralGreyLight = Color(0xFFE0E0E0);
  static const Color neutralGreyDark = Color(0xFF424242);

  /// Light color scheme configuration
  static const ColorScheme lightColorScheme = ColorScheme(
    brightness: Brightness.light,
    primary: primaryGreen,
    onPrimary: Colors.white,
    primaryContainer: primaryGreenLight,
    onPrimaryContainer: primaryGreenDark,
    secondary: secondaryBlue,
    onSecondary: Colors.white,
    secondaryContainer: secondaryBlueLight,
    onSecondaryContainer: secondaryBlueDark,
    tertiary: infoBlue,
    onTertiary: Colors.white,
    tertiaryContainer: Color(0xFFE3F2FD),
    onTertiaryContainer: Color(0xFF0D47A1),
    error: errorRed,
    onError: Colors.white,
    errorContainer: Color(0xFFFFEBEE),
    onErrorContainer: Color(0xFFB71C1C),
    surface: Colors.white,
    onSurface: Color(0xFF1C1B1F),
    surfaceContainerHighest: surfaceVariant,
    onSurfaceVariant: Color(0xFF49454F),
    outline: neutralGreyLight,
    outlineVariant: Color(0xFFCAC4D0),
    shadow: Colors.black,
    scrim: Colors.black,
    inverseSurface: Color(0xFF313033),
    onInverseSurface: Color(0xFFF4EFF4),
    inversePrimary: primaryGreenLight,
    surfaceTint: primaryGreen,
  );

  /// Dark color scheme configuration
  static const ColorScheme darkColorScheme = ColorScheme(
    brightness: Brightness.dark,
    primary: primaryGreenLight,
    onPrimary: primaryGreenDark,
    primaryContainer: primaryGreenDark,
    onPrimaryContainer: primaryGreenLight,
    secondary: secondaryBlueLight,
    onSecondary: secondaryBlueDark,
    secondaryContainer: secondaryBlueDark,
    onSecondaryContainer: secondaryBlueLight,
    tertiary: Color(0xFF90CAF9),
    onTertiary: Color(0xFF0D47A1),
    tertiaryContainer: Color(0xFF1565C0),
    onTertiaryContainer: Color(0xFFE3F2FD),
    error: Color(0xFFEF5350),
    onError: Color(0xFFB71C1C),
    errorContainer: Color(0xFFD32F2F),
    onErrorContainer: Color(0xFFFFEBEE),
    surface: surfaceDark,
    onSurface: Color(0xFFE6E1E5),
    surfaceContainerHighest: surfaceVariantDark,
    onSurfaceVariant: Color(0xFFCAC4D0),
    outline: Color(0xFF938F99),
    outlineVariant: Color(0xFF49454F),
    shadow: Colors.black,
    scrim: Colors.black,
    inverseSurface: Color(0xFFE6E1E5),
    onInverseSurface: Color(0xFF313033),
    inversePrimary: primaryGreen,
    surfaceTint: primaryGreenLight,
  );

  /// Status color variants for different UI states
  static const Map<String, Color> statusColors = {
    'success': successGreen,
    'warning': warningOrange,
    'error': errorRed,
    'info': infoBlue,
  };

  /// Get status color by name
  static Color getStatusColor(String status) {
    return statusColors[status] ?? neutralGrey;
  }

  /// Get appropriate text color for given background color
  static Color getTextColorForBackground(Color backgroundColor) {
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? Colors.black87 : Colors.white;
  }

  /// Generate color with opacity
  static Color withOpacity(Color color, double opacity) {
    return color.withValues(alpha: opacity);
  }

  /// Hover color for interactive elements
  static Color getHoverColor(Color baseColor) {
    return baseColor.withValues(alpha: 0.08);
  }

  /// Focus color for interactive elements
  static Color getFocusColor(Color baseColor) {
    return baseColor.withValues(alpha: 0.12);
  }

  /// Pressed color for interactive elements
  static Color getPressedColor(Color baseColor) {
    return baseColor.withValues(alpha: 0.16);
  }
}
