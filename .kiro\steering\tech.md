---
inclusion: always
---

# Technology Stack & Implementation Standards

## Core Framework Requirements
- **Flutter 3.7.2+**: Use latest stable version for cross-platform development
- **Dart SDK**: All code must use null-safety and latest language features
- **Primary Target**: Windows desktop (optimize UI for desktop-first experience)
- **Secondary Targets**: Android, iOS, Web (ensure responsive compatibility)

## Mandatory Architecture Patterns

### Clean Architecture (Strict Implementation)
```dart
// Domain entities must extend Equatable
class UserEntity extends Equatable {
  const UserEntity({required this.id, required this.name});
  final String id;
  final String name;
  @override
  List<Object> get props => [id, name];
}

// Repository pattern - abstract in domain, concrete in data
abstract class UserRepository {
  Future<Either<Failure, UserEntity>> getUser(String id);
}
```

### State Management (BLoC Pattern Only)
- Use `flutter_bloc 8.1.3` for ALL state management
- Cubit for simple state, Bloc for complex event-driven state
- NEVER use setState, Provider, or Riverpod
```dart
// Mandatory Cubit structure
class FeatureCubit extends Cubit<FeatureState> {
  FeatureCubit(this._repository) : super(FeatureInitial());
  final FeatureRepository _repository;
}
```

## Critical Dependencies (Exact Versions)
- `go_router: ^7.0.0` - Declarative routing ONLY
- `flutter_bloc: ^8.1.3` - State management
- `equatable: ^2.0.5` - All entities and states MUST extend Equatable
- `flutter_dotenv: ^5.1.0` - Environment configuration
- `logger: ^2.6.0` - Structured logging
- `shared_preferences: ^2.3.2` - Local storage

## Backend Integration Requirements

### Supabase Configuration
- Use environment variables for all Supabase configuration
- Implement Row Level Security (RLS) for all tables
- Use Supabase Auth with role-based permissions

### PowerSync (Offline-First Mandatory)
- ALL data operations must work offline
- Implement optimistic updates with rollback capability
- Use PowerSync for data synchronization, not direct Supabase calls

## Code Standards (Non-Negotiable)

### File Naming & Structure
- `snake_case` for all files and directories
- Suffixes: `_page.dart`, `_widget.dart`, `_cubit.dart`, `_model.dart`
- Barrel exports: Use `index.dart` in each feature module

### Import Organization (Mandatory Order)
```dart
// 1. Flutter/Dart imports
import 'package:flutter/material.dart';

// 2. Third-party packages
import 'package:flutter_bloc/flutter_bloc.dart';

// 3. Local imports (relative within feature, absolute across features)
import '../domain/entities/user_entity.dart';
import 'package:aplikasi_sppg/core/errors/failures.dart';
```

### Error Handling Pattern
```dart
// Use Either pattern for all repository methods
Future<Either<Failure, T>> methodName() async {
  try {
    final result = await dataSource.getData();
    return Right(result);
  } catch (e) {
    return Left(ServerFailure(message: e.toString()));
  }
}
```

## Development Workflow (Mandatory Steps)
1. Copy `.env.example` to `.env` and configure all variables
2. Run: `flutter run --dart-define-from-file=.env`
3. Use `flutter analyze` before committing (zero warnings allowed)
4. Run tests: `flutter test` (maintain >80% coverage)
5. Build: `flutter build windows --release` for production

## Performance Requirements (Measurable)
- Dashboard load time: <2 seconds on target hardware
- List pagination: 50 items per page maximum
- Offline sync: Complete within 30 seconds on reconnection
- Memory usage: <500MB on Windows desktop

## UI/UX Standards
- Use Fluent UI components exclusively
- Implement responsive breakpoints: mobile (<600px), tablet (600-1200px), desktop (>1200px)
- All interactive elements must have keyboard navigation support
- Minimum touch target size: 44px on mobile

## Testing Requirements (Mandatory)
- Unit tests for all business logic (domain layer)
- Widget tests for all reusable components
- Integration tests for critical user flows
- Mock all external dependencies using `mockito`

## Security & Permissions
- Implement role-based access using permission mixins
- Validate permissions on both client and server side
- Use Supabase RLS policies for data access control
- Log all permission-based access attempts