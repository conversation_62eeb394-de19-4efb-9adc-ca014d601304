# Navigation Components

This directory contains navigation components for the SOD-MBG application.

## AppSidebar

A responsive sidebar navigation component for desktop layouts that implements Material Design 3 NavigationRail with role-based menu items.

### Features

- **Responsive Design**: Adapts to different screen sizes
- **Role-based Permissions**: Shows/hides menu items based on user permissions
- **Expand/Collapse Animation**: Smooth animation when toggling sidebar state
- **Customizable**: Supports custom header and trailing widgets
- **Material Design 3**: Uses NavigationRail for consistent Material Design experience

### Usage

```dart
import 'package:aplikasi_sppg/app/design_system/components/navigation/app_sidebar.dart';

AppSidebar(
  selectedIndex: 0,
  isExpanded: true,
  onDestinationSelected: (index) {
    // Handle navigation
  },
  onExpandButtonPressed: () {
    // Toggle expanded state
  },
  items: [
    AppSidebarItem(
      icon: Icon(Icons.dashboard),
      label: 'Dashboard',
    ),
    AppSidebarItem(
      icon: Icon(Icons.restaurant),
      label: 'Kitchen Operations',
      requiredPermission: KitchenManagementPermission,
    ),
  ],
)
```

### AppSidebarItem

Represents a navigation item in the sidebar.

#### Properties

- `icon`: Widget to display as the icon
- `selectedIcon`: Optional widget to display when selected
- `label`: Text label for the item
- `requiredPermission`: Optional permission type required to view this item
- `badgeCount`: Optional badge count to display
- `onTap`: Optional callback when item is tapped

#### Permission Types

The following permission types are supported:
- `AdminPermission`
- `OversightPermission`
- `KitchenManagementPermission`
- `NutritionPermission`
- `FinancialPermission`
- `LogisticsPermission`

### AppSidebarScaffold

A scaffold widget that integrates the sidebar with main content.

```dart
AppSidebarScaffold(
  sidebar: AppSidebar(...),
  body: YourMainContent(),
  appBar: AppBar(title: Text('Your App')),
  showSidebar: true,
)
```

## Testing

The component includes comprehensive tests:

- Unit tests for component behavior
- Widget tests for user interactions
- Permission-based filtering tests
- Animation and state change tests

Run tests with:
```bash
flutter test test/app/design_system/components/navigation/
```

## Demo

A demo implementation is available at:
- `lib/features/demo/presentation/pages/sidebar_demo_page.dart`
- Route: `/demo/sidebar`

## Design System Integration

The component follows the app's design system:
- Uses `DesignTokens` for consistent styling
- Follows `AppSpacing` guidelines
- Integrates with the app's color scheme and typography
- Supports both light and dark themes