import 'package:flutter/material.dart';
import '../../tokens/design_tokens.dart';
import '../../theme/spacing.dart';
import '../../../../core/permissions/permission_mixins.dart';

/// A responsive sidebar navigation component for desktop layouts
/// Implements Material Design 3 NavigationRail with role-based menu items
class AppSidebar extends StatefulWidget {
  /// Current selected index
  final int selectedIndex;

  /// Callback when an item is selected
  final ValueChanged<int>? onDestinationSelected;

  /// Whether the sidebar is expanded or collapsed
  final bool isExpanded;

  /// Callback when the expand/collapse button is pressed
  final VoidCallback? onExpandButtonPressed;

  /// List of navigation items to display
  final List<AppSidebarItem> items;

  /// Optional header widget to display at the top of the sidebar
  final Widget? header;

  /// Optional trailing widget to display at the bottom of the sidebar
  final Widget? trailing;

  /// Background color of the sidebar
  final Color? backgroundColor;

  /// Width of the sidebar when expanded
  final double expandedWidth;

  /// Width of the sidebar when collapsed
  final double collapsedWidth;

  /// Duration of the expand/collapse animation
  final Duration animationDuration;

  const AppSidebar({
    super.key,
    required this.selectedIndex,
    required this.items,
    this.onDestinationSelected,
    this.isExpanded = true,
    this.onExpandButtonPressed,
    this.header,
    this.trailing,
    this.backgroundColor,
    this.expandedWidth = 280.0,
    this.collapsedWidth = 80.0,
    this.animationDuration = const Duration(milliseconds: 300),
  });

  @override
  State<AppSidebar> createState() => _AppSidebarState();
}

class _AppSidebarState extends State<AppSidebar> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return AnimatedContainer(
      duration: widget.animationDuration,
      width: widget.isExpanded ? widget.expandedWidth : widget.collapsedWidth,
      curve: DesignTokens.animationCurves['ease-out']!,
      child: Material(
        color: widget.backgroundColor ?? colorScheme.surface,
        elevation: 1,
        child: Column(
          children: [
            // Header section
            if (widget.header != null) widget.header!,

            // Expand/collapse button
            _buildExpandButton(colorScheme),

            // Navigation items
            Expanded(
              child: NavigationRail(
                selectedIndex: widget.selectedIndex,
                onDestinationSelected: widget.onDestinationSelected,
                labelType:
                    widget.isExpanded
                        ? NavigationRailLabelType.none
                        : NavigationRailLabelType.none,
                useIndicator: true,
                indicatorColor: colorScheme.secondaryContainer,
                backgroundColor: Colors.transparent,
                extended: widget.isExpanded,
                minWidth: widget.collapsedWidth,
                minExtendedWidth: widget.expandedWidth,
                destinations: _buildNavigationDestinations(),
              ),
            ),

            // Trailing widget
            if (widget.trailing != null) widget.trailing!,
          ],
        ),
      ),
    );
  }

  /// Builds the expand/collapse button
  Widget _buildExpandButton(ColorScheme colorScheme) {
    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: AppSpacing.sm,
        horizontal: widget.isExpanded ? AppSpacing.md : 0,
      ),
      child: Row(
        mainAxisAlignment:
            widget.isExpanded
                ? MainAxisAlignment.end
                : MainAxisAlignment.center,
        children: [
          IconButton(
            icon: Icon(
              widget.isExpanded ? Icons.chevron_left : Icons.chevron_right,
              color: colorScheme.onSurfaceVariant,
            ),
            onPressed: widget.onExpandButtonPressed,
            tooltip: widget.isExpanded ? 'Collapse' : 'Expand',
          ),
        ],
      ),
    );
  }

  /// Builds navigation destinations from items
  List<NavigationRailDestination> _buildNavigationDestinations() {
    return widget.items
        .where((item) => _shouldShowItem(item))
        .map(
          (item) => NavigationRailDestination(
            icon: item.icon,
            selectedIcon: item.selectedIcon ?? item.icon,
            label: Text(item.label),
            padding: EdgeInsets.symmetric(
              vertical: AppSpacing.sm,
              horizontal: AppSpacing.xs,
            ),
          ),
        )
        .toList();
  }

  /// Determines if an item should be shown based on permissions
  bool _shouldShowItem(AppSidebarItem item) {
    if (item.requiredPermission == null) {
      return true;
    }

    // Check permissions based on the required permission type
    if (item.requiredPermission == AdminPermission) {
      return context.hasPermission<AdminPermission>();
    } else if (item.requiredPermission == OversightPermission) {
      return context.hasPermission<OversightPermission>();
    } else if (item.requiredPermission == KitchenManagementPermission) {
      return context.hasPermission<KitchenManagementPermission>();
    } else if (item.requiredPermission == NutritionPermission) {
      return context.hasPermission<NutritionPermission>();
    } else if (item.requiredPermission == FinancialPermission) {
      return context.hasPermission<FinancialPermission>();
    } else if (item.requiredPermission == LogisticsPermission) {
      return context.hasPermission<LogisticsPermission>();
    }

    return true;
  }
}

/// Represents a navigation item in the sidebar
class AppSidebarItem {
  /// Icon to display
  final Widget icon;

  /// Selected icon to display (optional)
  final Widget? selectedIcon;

  /// Label text to display
  final String label;

  /// Required permission to view this item
  final Type? requiredPermission;

  /// Badge count to display (optional)
  final int? badgeCount;

  /// Whether this item is a divider
  final bool isDivider;

  /// Callback when this item is tapped
  final VoidCallback? onTap;

  /// Constructor for a regular navigation item
  const AppSidebarItem({
    required this.icon,
    required this.label,
    this.selectedIcon,
    this.requiredPermission,
    this.badgeCount,
    this.onTap,
  }) : isDivider = false;

  /// Constructor for a divider item
  const AppSidebarItem.divider()
    : icon = const SizedBox(),
      label = '',
      selectedIcon = null,
      requiredPermission = null,
      badgeCount = null,
      isDivider = true,
      onTap = null;
}

/// A widget that wraps the main content with a sidebar
class AppSidebarScaffold extends StatefulWidget {
  /// The main content to display
  final Widget body;

  /// The sidebar configuration
  final AppSidebar sidebar;

  /// Whether to show the sidebar
  final bool showSidebar;

  /// AppBar to display at the top
  final PreferredSizeWidget? appBar;

  /// FloatingActionButton to display
  final Widget? floatingActionButton;

  const AppSidebarScaffold({
    super.key,
    required this.body,
    required this.sidebar,
    this.showSidebar = true,
    this.appBar,
    this.floatingActionButton,
  });

  @override
  State<AppSidebarScaffold> createState() => _AppSidebarScaffoldState();
}

class _AppSidebarScaffoldState extends State<AppSidebarScaffold> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: widget.appBar,
      body: Row(
        children: [
          if (widget.showSidebar) widget.sidebar,
          Expanded(child: widget.body),
        ],
      ),
      floatingActionButton: widget.floatingActionButton,
    );
  }
}
