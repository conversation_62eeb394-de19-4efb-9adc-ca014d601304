import 'package:flutter/material.dart';
import '../components/navigation/responsive_app_bar.dart';
import '../../../core/permissions/permission_mixins.dart';
import '../../../core/auth/presentation/auth_service.dart';

/// Example usage of ResponsiveAppBar
class ResponsiveAppBarExample extends StatefulWidget {
  const ResponsiveAppBarExample({super.key});

  @override
  State<ResponsiveAppBarExample> createState() =>
      _ResponsiveAppBarExampleState();
}

class _ResponsiveAppBarExampleState extends State<ResponsiveAppBarExample> {
  int _selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    // Ensure AuthService is initialized
    _initializeAuthService();
  }

  Future<void> _initializeAuthService() async {
    if (!AuthService.instance.isInitialized) {
      await AuthService.instance.initialize();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ResponsiveAppBar(
        title: 'SOD-MBG',
        centerTitle: false,
        selectedIndex: _selectedIndex,
        onNavigationItemSelected: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        navigationItems: const [
          ResponsiveAppBarItem(
            label: 'Dashboard',
            icon: Icons.dashboard,
            permissionType: AdminPermission,
          ),
          ResponsiveAppBarItem(
            label: 'Operasi Dapur',
            icon: Icons.restaurant,
            permissionType: KitchenManagementPermission,
          ),
          ResponsiveAppBarItem(
            label: 'Menu & Nutrisi',
            icon: Icons.set_meal,
            permissionType: NutritionPermission,
          ),
          ResponsiveAppBarItem(
            label: 'Keuangan',
            icon: Icons.attach_money,
            permissionType: FinancialPermission,
          ),
          ResponsiveAppBarItem(
            label: 'Logistik',
            icon: Icons.local_shipping,
            permissionType: LogisticsPermission,
          ),
        ],
        actions: [
          IconButton(icon: const Icon(Icons.notifications), onPressed: () {}),
          IconButton(icon: const Icon(Icons.account_circle), onPressed: () {}),
        ],
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Halaman $_selectedIndex',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                // Show a dialog with current user info
                final user = AuthService.instance.currentUser;
                showDialog(
                  context: context,
                  builder:
                      (context) => AlertDialog(
                        title: const Text('User Info'),
                        content:
                            user != null
                                ? Text(
                                  'Logged in as: ${user.displayName}\n'
                                  'Role: ${user.roleDisplayName}',
                                )
                                : const Text('Not logged in'),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(),
                            child: const Text('Close'),
                          ),
                        ],
                      ),
                );
              },
              child: const Text('Show User Info'),
            ),
          ],
        ),
      ),
    );
  }
}
