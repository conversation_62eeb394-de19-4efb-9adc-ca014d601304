import 'package:flutter/material.dart';
import '../../../../app/design_system/components/navigation/app_sidebar.dart';
import '../../../../../core/permissions/permission_mixins.dart';

/// Demo page to showcase the AppSidebar component
class SidebarDemoPage extends StatefulWidget {
  const SidebarDemoPage({super.key});

  @override
  State<SidebarDemoPage> createState() => _SidebarDemoPageState();
}

class _SidebarDemoPageState extends State<SidebarDemoPage> {
  int _selectedIndex = 0;
  bool _isExpanded = true;
  final List<String> _pageContents = [
    'Dashboard Content',
    'Kitchen Operations Content',
    'Inventory Management Content',
    'Menu Planning Content',
    'Reports Content',
    'Settings Content',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Sidebar Navigation Demo')),
      body: AppSidebarScaffold(
        sidebar: AppSidebar(
          selectedIndex: _selectedIndex,
          isExpanded: _isExpanded,
          onDestinationSelected: (index) {
            setState(() {
              _selectedIndex = index;
            });
          },
          onExpandButtonPressed: () {
            setState(() {
              _isExpanded = !_isExpanded;
            });
          },
          header: _buildHeader(),
          trailing: _buildTrailing(),
          items: _getNavigationItems(),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                _pageContents[_selectedIndex],
                style: Theme.of(context).textTheme.headlineMedium,
              ),
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _isExpanded = !_isExpanded;
                  });
                },
                child: Text(
                  _isExpanded ? 'Collapse Sidebar' : 'Expand Sidebar',
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment:
            _isExpanded ? CrossAxisAlignment.start : CrossAxisAlignment.center,
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.restaurant,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
          ),
          if (_isExpanded) ...[
            const SizedBox(height: 8),
            Text(
              'SOD-MBG',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            Text(
              'Sistem Operasional Dapur',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTrailing() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          const Divider(),
          ListTile(
            leading: const Icon(Icons.person),
            title: _isExpanded ? const Text('Profile') : null,
            contentPadding:
                _isExpanded
                    ? const EdgeInsets.symmetric(horizontal: 8)
                    : EdgeInsets.zero,
            minLeadingWidth: 0,
            horizontalTitleGap: 8,
            onTap: () {},
          ),
          ListTile(
            leading: const Icon(Icons.logout),
            title: _isExpanded ? const Text('Logout') : null,
            contentPadding:
                _isExpanded
                    ? const EdgeInsets.symmetric(horizontal: 8)
                    : EdgeInsets.zero,
            minLeadingWidth: 0,
            horizontalTitleGap: 8,
            onTap: () {},
          ),
        ],
      ),
    );
  }

  List<AppSidebarItem> _getNavigationItems() {
    return [
      const AppSidebarItem(icon: Icon(Icons.dashboard), label: 'Dashboard'),
      const AppSidebarItem(
        icon: Icon(Icons.restaurant),
        label: 'Kitchen Operations',
        requiredPermission: KitchenManagementPermission,
      ),
      const AppSidebarItem(
        icon: Icon(Icons.inventory),
        label: 'Inventory',
        requiredPermission: KitchenManagementPermission,
      ),
      const AppSidebarItem(
        icon: Icon(Icons.menu_book),
        label: 'Menu Planning',
        requiredPermission: NutritionPermission,
      ),
      const AppSidebarItem(icon: Icon(Icons.bar_chart), label: 'Reports'),
      const AppSidebarItem(icon: Icon(Icons.settings), label: 'Settings'),
    ];
  }
}
