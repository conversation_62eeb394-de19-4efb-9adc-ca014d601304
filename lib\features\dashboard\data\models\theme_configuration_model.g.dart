// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'theme_configuration_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ThemeConfigurationModel _$ThemeConfigurationModelFromJson(
  Map<String, dynamic> json,
) => ThemeConfigurationModel(
  accentColorHex: json['accent_color_hex'] as String?,
  cardColorHex: json['card_color_hex'] as String?,
  textStyles: (json['text_styles'] as Map<String, dynamic>?)?.map(
    (k, e) => MapEntry(k, TextStyleModel.fromJson(e as Map<String, dynamic>)),
  ),
);

Map<String, dynamic> _$ThemeConfigurationModelToJson(
  ThemeConfigurationModel instance,
) => <String, dynamic>{
  'accent_color_hex': instance.accentColorHex,
  'card_color_hex': instance.cardColorHex,
  'text_styles': instance.textStyles?.map((k, e) => MapEntry(k, e.toJson())),
};

TextStyleModel _$TextStyleModelFromJson(Map<String, dynamic> json) =>
    TextStyleModel(
      fontSize: (json['font_size'] as num?)?.toDouble(),
      fontWeight: TextStyleModel._fontWeightFromJson(
        (json['font_weight'] as num?)?.toInt(),
      ),
      fontFamily: json['font_family'] as String?,
      colorHex: json['color_hex'] as String?,
      letterSpacing: (json['letter_spacing'] as num?)?.toDouble(),
      height: (json['height'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$TextStyleModelToJson(TextStyleModel instance) =>
    <String, dynamic>{
      'font_size': instance.fontSize,
      'font_weight': TextStyleModel._fontWeightToJson(instance.fontWeight),
      'font_family': instance.fontFamily,
      'color_hex': instance.colorHex,
      'letter_spacing': instance.letterSpacing,
      'height': instance.height,
    };
