import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'app_bottom_navigation.dart';
import 'navigation_items.dart';
import '../../../../core/permissions/permission_mixins.dart';

/// Example implementation of AppBottomNavigation
/// Demonstrates how to integrate bottom navigation with routing and permissions
class AppBottomNavigationExample extends StatefulWidget {
  const AppBottomNavigationExample({super.key});

  @override
  State<AppBottomNavigationExample> createState() =>
      _AppBottomNavigationExampleState();
}

class _AppBottomNavigationExampleState
    extends State<AppBottomNavigationExample> {
  int _selectedIndex = 0;

  // Simulate notification counts
  int _kitchenNotifications = 3;
  int _inventoryAlerts = 1;
  int _logisticsUpdates = 0;
  int _reportsPending = 2;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Bottom Navigation Example'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add_alert),
            onPressed: _simulateNotificationUpdate,
            tooltip: 'Simulate Notification Update',
          ),
        ],
      ),
      body: _buildBody(),
      bottomNavigationBar: _buildBottomNavigation(),
    );
  }

  Widget _buildBody() {
    switch (_selectedIndex) {
      case 0:
        return _buildDashboardContent();
      case 1:
        return _buildKitchenContent();
      case 2:
        return _buildInventoryContent();
      case 3:
        return _buildLogisticsContent();
      case 4:
        return _buildReportsContent();
      default:
        return _buildDashboardContent();
    }
  }

  Widget _buildBottomNavigation() {
    // Get navigation items with current badge counts
    final navigationItems = NavigationItems.getBottomNavigationItemsWithBadges(
      kitchenNotifications: _kitchenNotifications,
      inventoryAlerts: _inventoryAlerts,
      logisticsUpdates: _logisticsUpdates,
      reportsPending: _reportsPending,
    );

    return AppBottomNavigation(
      selectedIndex: _selectedIndex,
      items: navigationItems,
      onDestinationSelected: (index) {
        setState(() {
          _selectedIndex = index;
        });

        // Optional: Navigate using GoRouter
        final routePath = NavigationItems.getRoutePathForIndex(index);
        if (routePath != null) {
          // context.go(routePath);
        }
      },
    );
  }

  Widget _buildDashboardContent() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.dashboard, size: 64, color: Colors.blue),
          SizedBox(height: 16),
          Text(
            'Dashboard',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text('Halaman utama dengan ringkasan operasional'),
        ],
      ),
    );
  }

  Widget _buildKitchenContent() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.kitchen, size: 64, color: Colors.green),
          const SizedBox(height: 16),
          const Text(
            'Manajemen Dapur',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          const Text('Kelola operasi dapur harian'),
          const SizedBox(height: 16),
          if (_kitchenNotifications > 0)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange),
              ),
              child: Text(
                '$_kitchenNotifications notifikasi baru',
                style: const TextStyle(color: Colors.orange),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildInventoryContent() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.inventory_2, size: 64, color: Colors.purple),
          const SizedBox(height: 16),
          const Text(
            'Manajemen Inventori',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          const Text('Kelola stok dan bahan baku'),
          const SizedBox(height: 16),
          if (_inventoryAlerts > 0)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red),
              ),
              child: Text(
                '$_inventoryAlerts peringatan stok',
                style: const TextStyle(color: Colors.red),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildLogisticsContent() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.local_shipping, size: 64, color: Colors.teal),
          SizedBox(height: 16),
          Text(
            'Manajemen Logistik',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text('Kelola distribusi dan pengiriman'),
        ],
      ),
    );
  }

  Widget _buildReportsContent() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.assessment, size: 64, color: Colors.indigo),
          const SizedBox(height: 16),
          const Text(
            'Laporan dan Analisis',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          const Text('Lihat laporan operasional'),
          const SizedBox(height: 16),
          if (_reportsPending > 0)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue),
              ),
              child: Text(
                '$_reportsPending laporan menunggu review',
                style: const TextStyle(color: Colors.blue),
              ),
            ),
        ],
      ),
    );
  }

  void _simulateNotificationUpdate() {
    setState(() {
      // Simulate random notification updates
      _kitchenNotifications = (_kitchenNotifications + 1) % 10;
      _inventoryAlerts = (_inventoryAlerts + 1) % 5;
      _logisticsUpdates = (_logisticsUpdates + 1) % 3;
      _reportsPending = (_reportsPending + 1) % 8;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Notifikasi diperbarui'),
        duration: Duration(seconds: 1),
      ),
    );
  }
}

/// Example of using AppBottomNavigationScaffold
class AppBottomNavigationScaffoldExample extends StatelessWidget {
  const AppBottomNavigationScaffoldExample({super.key});

  @override
  Widget build(BuildContext context) {
    return AppBottomNavigationScaffold(
      appBar: AppBar(title: const Text('Bottom Navigation Scaffold')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.phone_android, size: 64),
            SizedBox(height: 16),
            Text(
              'Mobile Layout',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text('Bottom navigation akan muncul di perangkat mobile'),
            SizedBox(height: 16),
            Text(
              'Coba ubah ukuran layar untuk melihat perbedaannya',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      ),
      navigationItems: NavigationItems.getBottomNavigationItems(),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('FAB pressed')));
        },
        child: const Icon(Icons.add),
      ),
    );
  }
}

/// Example showing permission-based navigation
class PermissionBasedNavigationExample extends StatelessWidget {
  const PermissionBasedNavigationExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Permission-Based Navigation')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.security, size: 64, color: Colors.amber),
            SizedBox(height: 16),
            Text(
              'Navigation dengan Izin',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              'Item navigasi akan muncul berdasarkan izin pengguna',
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16),
            Text(
              'Login sebagai pengguna dengan peran berbeda untuk melihat perbedaan menu',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      ),
      bottomNavigationBar: AppBottomNavigation(
        selectedIndex: 0,
        items: [
          AppBottomNavigationItem(
            icon: const Icon(Icons.dashboard),
            label: 'Dashboard',
            tooltip: 'Semua pengguna dapat melihat',
          ),
          AppBottomNavigationItem(
            icon: const Icon(Icons.kitchen),
            label: 'Dapur',
            tooltip: 'Hanya untuk pengelola dapur',
            requiredPermission: KitchenManagementPermission,
          ),
          AppBottomNavigationItem(
            icon: const Icon(Icons.admin_panel_settings),
            label: 'Admin',
            tooltip: 'Hanya untuk administrator',
            requiredPermission: AdminPermission,
          ),
        ],
        onDestinationSelected: (index) {
          // Handle navigation
        },
      ),
    );
  }
}

/// Example showing custom styling
class CustomStyledNavigationExample extends StatelessWidget {
  const CustomStyledNavigationExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Custom Styled Navigation')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.palette, size: 64, color: Colors.pink),
            SizedBox(height: 16),
            Text(
              'Custom Styling',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text('Bottom navigation dengan styling kustom'),
          ],
        ),
      ),
      bottomNavigationBar: AppBottomNavigation(
        selectedIndex: 0,
        items: NavigationItems.getBottomNavigationItems(),
        backgroundColor: Colors.grey[100],
        height: 90,
        showLabels: true,
        animationDuration: const Duration(milliseconds: 300),
        onDestinationSelected: (index) {
          // Handle navigation
        },
      ),
    );
  }
}
