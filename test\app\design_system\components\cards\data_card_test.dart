import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:aplikasi_sppg/app/design_system/components/cards/data_card.dart';
import 'package:aplikasi_sppg/app/design_system/theme/app_theme.dart';

void main() {
  group('DataCard Widget Tests', () {
    Widget createTestWidget({required Widget child, double? screenWidth}) {
      return MaterialApp(
        theme: AppTheme.lightTheme,
        home: Scaffold(
          body:
              screenWidth != null
                  ? SizedBox(width: screenWidth, child: child)
                  : child,
        ),
      );
    }

    group('Basic Functionality', () {
      testWidgets('displays title and content correctly', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const DataCard(title: 'Test Title', content: 'Test Content'),
          ),
        );

        expect(find.text('Test Title'), findsOneWidget);
        expect(find.text('Test Content'), findsOneWidget);
      });

      testWidgets('displays subtitle when provided', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const DataCard(
              title: 'Test Title',
              content: 'Test Content',
              subtitle: 'Test Subtitle',
            ),
          ),
        );

        expect(find.text('Test Title'), findsOneWidget);
        expect(find.text('Test Content'), findsOneWidget);
        expect(find.text('Test Subtitle'), findsOneWidget);
      });

      testWidgets('displays icon when provided', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const DataCard(
              title: 'Test Title',
              content: 'Test Content',
              icon: Icons.info,
            ),
          ),
        );

        expect(find.byIcon(Icons.info), findsOneWidget);
      });

      testWidgets('displays trailing widget when provided', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: DataCard(
              title: 'Test Title',
              content: 'Test Content',
              trailing: Container(
                key: const Key('trailing_widget'),
                child: const Icon(Icons.arrow_forward),
              ),
            ),
          ),
        );

        expect(find.byKey(const Key('trailing_widget')), findsOneWidget);
        expect(find.byIcon(Icons.arrow_forward), findsOneWidget);
      });
    });

    group('Layout Variants', () {
      testWidgets('creates horizontal layout correctly', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: DataCard.horizontal(
              title: 'Horizontal Title',
              content: 'Horizontal Content',
            ),
          ),
        );

        expect(find.text('Horizontal Title'), findsOneWidget);
        expect(find.text('Horizontal Content'), findsOneWidget);

        // Verify horizontal layout by checking Row widget
        expect(find.byType(Row), findsWidgets);
      });

      testWidgets('creates vertical layout correctly', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: DataCard.vertical(
              title: 'Vertical Title',
              content: 'Vertical Content',
              showDivider: true,
            ),
          ),
        );

        expect(find.text('Vertical Title'), findsOneWidget);
        expect(find.text('Vertical Content'), findsOneWidget);

        // Verify vertical layout by checking Column widget
        expect(find.byType(Column), findsWidgets);

        // Verify divider is shown
        expect(find.byType(Divider), findsOneWidget);
      });
    });

    group('Responsive Behavior', () {
      testWidgets('adapts to mobile screen size', (tester) async {
        const mobileWidth = 400.0;

        await tester.pumpWidget(
          createTestWidget(
            screenWidth: mobileWidth,
            child: DataCard.horizontal(
              title: 'Mobile Test',
              content: 'Mobile Content',
            ),
          ),
        );

        await tester.pumpAndSettle();

        expect(find.text('Mobile Test'), findsOneWidget);
        expect(find.text('Mobile Content'), findsOneWidget);

        // On mobile, horizontal layout should be forced to vertical
        // This is tested by checking the layout structure
        final dataCard = tester.widget<DataCard>(find.byType(DataCard));
        expect(dataCard.layout, equals(DataCardLayout.horizontal));
      });

      testWidgets('adapts to tablet screen size', (tester) async {
        const tabletWidth = 800.0;

        await tester.pumpWidget(
          createTestWidget(
            screenWidth: tabletWidth,
            child: const DataCard(
              title: 'Tablet Test',
              content: 'Tablet Content',
              layout: DataCardLayout.vertical,
            ),
          ),
        );

        await tester.pumpAndSettle();

        expect(find.text('Tablet Test'), findsOneWidget);
        expect(find.text('Tablet Content'), findsOneWidget);
      });

      testWidgets('adapts to desktop screen size', (tester) async {
        const desktopWidth = 1200.0;

        await tester.pumpWidget(
          createTestWidget(
            screenWidth: desktopWidth,
            child: const DataCard(
              title: 'Desktop Test',
              content: 'Desktop Content',
              layout: DataCardLayout.horizontal,
            ),
          ),
        );

        await tester.pumpAndSettle();

        expect(find.text('Desktop Test'), findsOneWidget);
        expect(find.text('Desktop Content'), findsOneWidget);
      });
    });

    group('Interaction Tests', () {
      testWidgets('handles tap correctly', (tester) async {
        bool tapped = false;

        await tester.pumpWidget(
          createTestWidget(
            child: DataCard(
              title: 'Tappable Card',
              content: 'Tap me',
              onTap: () {
                tapped = true;
              },
            ),
          ),
        );

        await tester.tap(find.byType(DataCard));
        await tester.pumpAndSettle();

        expect(tapped, isTrue);
      });

      testWidgets('shows hover animation when enabled', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const DataCard(
              title: 'Hover Card',
              content: 'Hover me',
              animateOnHover: true,
            ),
          ),
        );

        // Find the MouseRegion widgets (there may be multiple in the widget tree)
        expect(find.byType(MouseRegion), findsWidgets);

        // Simulate hover
        final gesture = await tester.createGesture();
        await gesture.addPointer(location: Offset.zero);
        addTearDown(gesture.removePointer);

        await gesture.moveTo(tester.getCenter(find.byType(DataCard)));
        await tester.pumpAndSettle();

        // Animation should be triggered (tested by presence of AnimatedBuilder)
        expect(find.byType(AnimatedBuilder), findsOneWidget);
      });

      testWidgets('does not animate when hover animation is disabled', (
        tester,
      ) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const DataCard(
              title: 'No Hover Card',
              content: 'No hover animation',
              animateOnHover: false,
            ),
          ),
        );

        // MouseRegion should still be present but animation won't trigger
        expect(find.byType(MouseRegion), findsWidgets);
      });
    });

    group('Styling Tests', () {
      testWidgets('applies custom background color', (tester) async {
        const customColor = Colors.red;

        await tester.pumpWidget(
          createTestWidget(
            child: const DataCard(
              title: 'Custom Background',
              content: 'Red background',
              backgroundColor: customColor,
            ),
          ),
        );

        final card = tester.widget<Card>(find.byType(Card));
        expect(card.color, equals(customColor));
      });

      testWidgets('applies custom border color', (tester) async {
        const customBorderColor = Colors.blue;

        await tester.pumpWidget(
          createTestWidget(
            child: const DataCard(
              title: 'Custom Border',
              content: 'Blue border',
              borderColor: customBorderColor,
            ),
          ),
        );

        final card = tester.widget<Card>(find.byType(Card));
        final shape = card.shape as RoundedRectangleBorder;
        expect(shape.side.color, equals(customBorderColor));
      });

      testWidgets('has consistent elevation and shadow', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const DataCard(
              title: 'Elevation Test',
              content: 'Check elevation',
            ),
          ),
        );

        final card = tester.widget<Card>(find.byType(Card));
        expect(card.elevation, isNotNull);
        expect(card.elevation! >= 2.0, isTrue);
      });

      testWidgets('has rounded corners', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const DataCard(
              title: 'Border Radius Test',
              content: 'Check corners',
            ),
          ),
        );

        final card = tester.widget<Card>(find.byType(Card));
        final shape = card.shape as RoundedRectangleBorder;
        expect(shape.borderRadius, isA<BorderRadius>());
      });
    });

    group('Accessibility Tests', () {
      testWidgets('is accessible for screen readers', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const DataCard(
              title: 'Accessible Card',
              content: 'Screen reader friendly',
            ),
          ),
        );

        // Check that text is properly exposed for screen readers
        expect(find.text('Accessible Card'), findsOneWidget);
        expect(find.text('Screen reader friendly'), findsOneWidget);

        // Verify semantic structure
        expect(tester.getSemantics(find.byType(DataCard)), isNotNull);
      });

      testWidgets('supports keyboard navigation when tappable', (tester) async {
        bool tapped = false;

        await tester.pumpWidget(
          createTestWidget(
            child: DataCard(
              title: 'Keyboard Card',
              content: 'Keyboard accessible',
              onTap: () {
                tapped = true;
              },
            ),
          ),
        );

        // Focus the card
        await tester.sendKeyEvent(LogicalKeyboardKey.tab);
        await tester.pumpAndSettle();

        // Activate with Enter key
        await tester.sendKeyEvent(LogicalKeyboardKey.enter);
        await tester.pumpAndSettle();

        // Note: This test might need adjustment based on actual focus behavior
        // The important thing is that the card is tappable and accessible
        expect(find.byType(GestureDetector), findsOneWidget);
      });
    });

    group('Edge Cases', () {
      testWidgets('handles very long text gracefully', (tester) async {
        const longTitle =
            'This is a very long title that should be truncated properly to avoid overflow issues in the UI';
        const longContent =
            'This is very long content that should wrap or truncate appropriately based on the available space in the card layout';

        await tester.pumpWidget(
          createTestWidget(
            child: const DataCard(title: longTitle, content: longContent),
          ),
        );

        expect(
          find.textContaining('This is a very long title'),
          findsOneWidget,
        );
        expect(
          find.textContaining('This is very long content'),
          findsOneWidget,
        );

        // Verify no overflow
        expect(tester.takeException(), isNull);
      });

      testWidgets('handles empty content gracefully', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const DataCard(title: 'Empty Content', content: ''),
          ),
        );

        expect(find.text('Empty Content'), findsOneWidget);
        expect(find.text(''), findsOneWidget);
        expect(tester.takeException(), isNull);
      });

      testWidgets('handles null optional parameters', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            child: const DataCard(
              title: 'Minimal Card',
              content: 'Basic content',
              // All optional parameters are null by default
            ),
          ),
        );

        expect(find.text('Minimal Card'), findsOneWidget);
        expect(find.text('Basic content'), findsOneWidget);
        expect(tester.takeException(), isNull);
      });
    });
  });
}
