import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:aplikasi_sppg/app/design_system/components/navigation/responsive_app_bar.dart';
import 'package:aplikasi_sppg/core/auth/presentation/auth_service.dart';
import 'package:aplikasi_sppg/core/auth/domain/simplified_app_user.dart';

// Mock classes for testing
class MockAuthService extends Mock implements AuthService {
  @override
  AppUser? get currentUser => null;

  @override
  bool get isAdminYayasan => false;

  @override
  bool get isKepalaDapur => false;
}

class MockBuildContext extends Mock implements BuildContext {}

void main() {
  group('ResponsiveAppBar Widget Tests', () {
    testWidgets('renders correctly on desktop', (WidgetTester tester) async {
      // Set desktop screen size
      tester.view.physicalSize = const Size(1280, 800);
      tester.view.devicePixelRatio = 1.0;

      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            colorScheme: ColorScheme.light(
              primary: Colors.green,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
              primaryContainer: Colors.green.shade100,
            ),
          ),
          home: Scaffold(
            appBar: ResponsiveAppBar(
              title: 'Desktop Title',
              navigationItems: const [
                ResponsiveAppBarItem(label: 'Home', icon: Icons.home),
                ResponsiveAppBarItem(label: 'Profile', icon: Icons.person),
              ],
            ),
          ),
        ),
      );

      // Verify desktop layout shows navigation items
      expect(find.text('Desktop Title'), findsOneWidget);
      expect(find.text('Home'), findsOneWidget);
      expect(find.text('Profile'), findsOneWidget);

      // Reset screen size
      addTearDown(tester.view.reset);
    });

    testWidgets('handles selected item styling', (WidgetTester tester) async {
      // Set desktop screen size
      tester.view.physicalSize = const Size(1280, 800);
      tester.view.devicePixelRatio = 1.0;

      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            colorScheme: ColorScheme.light(
              primary: Colors.green,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
              primaryContainer: Colors.green.shade100,
            ),
          ),
          home: Scaffold(
            appBar: ResponsiveAppBar(
              title: 'Test Title',
              selectedIndex: 1,
              navigationItems: const [
                ResponsiveAppBarItem(label: 'Home', icon: Icons.home),
                ResponsiveAppBarItem(label: 'Profile', icon: Icons.person),
              ],
            ),
          ),
        ),
      );

      // Find the TextButtons for navigation items
      final homeButton = find.widgetWithText(TextButton, 'Home');
      final profileButton = find.widgetWithText(TextButton, 'Profile');

      expect(homeButton, findsOneWidget);
      expect(profileButton, findsOneWidget);

      // Reset screen size
      addTearDown(tester.view.reset);
    });

    testWidgets('shows/hides title based on showTitle property', (
      WidgetTester tester,
    ) async {
      // Test with title shown
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            appBar: ResponsiveAppBar(title: 'Visible Title', showTitle: true),
          ),
        ),
      );
      expect(find.text('Visible Title'), findsOneWidget);

      // Test with title hidden
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            appBar: ResponsiveAppBar(title: 'Hidden Title', showTitle: false),
          ),
        ),
      );
      expect(find.text('Hidden Title'), findsNothing);
    });

    testWidgets('renders custom title widget when provided', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            appBar: ResponsiveAppBar(
              title: 'Regular Title',
              titleWidget: const Text('Custom Title Widget'),
            ),
          ),
        ),
      );

      expect(find.text('Regular Title'), findsNothing);
      expect(find.text('Custom Title Widget'), findsOneWidget);
    });

    testWidgets('renders bottom widget when provided', (
      WidgetTester tester,
    ) async {
      final tabBar = TabBar(
        tabs: [Tab(text: 'Tab 1'), Tab(text: 'Tab 2')],
        controller: TabController(length: 2, vsync: const TestVSync()),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            appBar: ResponsiveAppBar(title: 'Title with Tabs', bottom: tabBar),
          ),
        ),
      );

      expect(find.text('Tab 1'), findsOneWidget);
      expect(find.text('Tab 2'), findsOneWidget);
    });
  });
}
