import 'package:flutter/material.dart';
import '../../theme/color_scheme.dart';
import '../../theme/spacing.dart';
import '../../theme/typography.dart';

/// A card widget for displaying metric information on dashboards.
///
/// This widget displays a metric with a title, value, and optional icon.
/// It supports different variants (success, warning, error, info) and
/// includes hover and tap animations for interactivity.
class MetricCard extends StatefulWidget {
  /// The title of the metric
  final String title;

  /// The value to display (can be a number, percentage, or any string)
  final String value;

  /// Optional icon to display with the metric
  final IconData? icon;

  /// Optional subtitle or description text
  final String? subtitle;

  /// The variant of the card (success, warning, error, info)
  final String variant;

  /// Optional callback when the card is tapped
  final VoidCallback? onTap;

  /// Whether to show a subtle animation on hover
  final bool animateOnHover;

  /// Creates a MetricCard widget.
  ///
  /// [title] is the name of the metric.
  /// [value] is the value to display.
  /// [icon] is an optional icon to display.
  /// [subtitle] is an optional description text.
  /// [variant] determines the color scheme (success, warning, error, info).
  /// [onTap] is called when the card is tapped.
  /// [animateOnHover] determines whether to show hover animations.
  const MetricCard({
    super.key,
    required this.title,
    required this.value,
    this.icon,
    this.subtitle,
    this.variant = 'info',
    this.onTap,
    this.animateOnHover = true,
  });

  /// Creates a success variant of MetricCard.
  factory MetricCard.success({
    required String title,
    required String value,
    IconData? icon,
    String? subtitle,
    VoidCallback? onTap,
    bool animateOnHover = true,
  }) {
    return MetricCard(
      title: title,
      value: value,
      icon: icon,
      subtitle: subtitle,
      variant: 'success',
      onTap: onTap,
      animateOnHover: animateOnHover,
    );
  }

  /// Creates a warning variant of MetricCard.
  factory MetricCard.warning({
    required String title,
    required String value,
    IconData? icon,
    String? subtitle,
    VoidCallback? onTap,
    bool animateOnHover = true,
  }) {
    return MetricCard(
      title: title,
      value: value,
      icon: icon,
      subtitle: subtitle,
      variant: 'warning',
      onTap: onTap,
      animateOnHover: animateOnHover,
    );
  }

  /// Creates an error variant of MetricCard.
  factory MetricCard.error({
    required String title,
    required String value,
    IconData? icon,
    String? subtitle,
    VoidCallback? onTap,
    bool animateOnHover = true,
  }) {
    return MetricCard(
      title: title,
      value: value,
      icon: icon,
      subtitle: subtitle,
      variant: 'error',
      onTap: onTap,
      animateOnHover: animateOnHover,
    );
  }

  /// Creates an info variant of MetricCard.
  factory MetricCard.info({
    required String title,
    required String value,
    IconData? icon,
    String? subtitle,
    VoidCallback? onTap,
    bool animateOnHover = true,
  }) {
    return MetricCard(
      title: title,
      value: value,
      icon: icon,
      subtitle: subtitle,
      variant: 'info',
      onTap: onTap,
      animateOnHover: animateOnHover,
    );
  }

  @override
  State<MetricCard> createState() => _MetricCardState();
}

class _MetricCardState extends State<MetricCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.02).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _elevationAnimation = Tween<double>(begin: 2.0, end: 4.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Color _getVariantColor() {
    switch (widget.variant.toLowerCase()) {
      case 'success':
        return AppColorScheme.successGreen;
      case 'warning':
        return AppColorScheme.warningOrange;
      case 'error':
        return AppColorScheme.errorRed;
      case 'info':
      default:
        return AppColorScheme.infoBlue;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final variantColor = _getVariantColor();

    return MouseRegion(
      onEnter: (_) {
        if (widget.animateOnHover) {
          _animationController.forward();
        }
      },
      onExit: (_) {
        if (widget.animateOnHover) {
          _animationController.reverse();
        }
      },
      child: GestureDetector(
        onTap: widget.onTap,
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Card(
                elevation: _elevationAnimation.value,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: BorderSide(
                    color: variantColor.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: child,
              ),
            );
          },
          child: Container(
            padding: AppSpacing.allMd,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  colorScheme.surface,
                  colorScheme.surface,
                  variantColor.withValues(alpha: 0.05),
                ],
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        widget.title,
                        style: AppTypography.metricLabel.copyWith(
                          color: colorScheme.onSurfaceVariant,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (widget.icon != null)
                      Icon(widget.icon, color: variantColor, size: 20),
                  ],
                ),
                AppSpacing.verticalSpaceSm,
                Text(
                  widget.value,
                  style: AppTypography.metricValue.copyWith(
                    color: colorScheme.onSurface,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (widget.subtitle != null) ...[
                  AppSpacing.verticalSpaceXs,
                  Text(
                    widget.subtitle!,
                    style: AppTypography.helperMessage.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
                AppSpacing.verticalSpaceSm,
                Container(
                  height: 4,
                  width: 40,
                  decoration: BoxDecoration(
                    color: variantColor,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
