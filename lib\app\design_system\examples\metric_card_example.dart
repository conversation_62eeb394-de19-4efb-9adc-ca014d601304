import 'package:flutter/material.dart';
import '../components/cards/metric_card.dart';
import '../theme/spacing.dart';

/// Example page showcasing different variants of MetricCard
class MetricCardExample extends StatelessWidget {
  const MetricCardExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Metric Card Examples')),
      body: SingleChildScrollView(
        padding: AppSpacing.allMd,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Metric Cards',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            AppSpacing.verticalSpaceMd,
            const Text(
              'Standard Variants',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
            ),
            AppSpacing.verticalSpaceSm,
            GridView.count(
              crossAxisCount: 2,
              mainAxisSpacing: AppSpacing.md,
              crossAxisSpacing: AppSpacing.md,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                MetricCard.success(
                  title: 'Total Meals Served',
                  value: '1,234',
                  icon: Icons.restaurant,
                  subtitle: 'Increased by 5% this week',
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Success card tapped')),
                    );
                  },
                ),
                MetricCard.warning(
                  title: 'Inventory Status',
                  value: '75%',
                  icon: Icons.inventory,
                  subtitle: 'Some items running low',
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Warning card tapped')),
                    );
                  },
                ),
                MetricCard.error(
                  title: 'Late Deliveries',
                  value: '3',
                  icon: Icons.delivery_dining,
                  subtitle: 'Action required',
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Error card tapped')),
                    );
                  },
                ),
                MetricCard.info(
                  title: 'Average Preparation Time',
                  value: '45 min',
                  icon: Icons.timer,
                  subtitle: 'Stable performance',
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Info card tapped')),
                    );
                  },
                ),
              ],
            ),
            AppSpacing.verticalSpaceLg,
            const Text(
              'Without Icons',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
            ),
            AppSpacing.verticalSpaceSm,
            GridView.count(
              crossAxisCount: 2,
              mainAxisSpacing: AppSpacing.md,
              crossAxisSpacing: AppSpacing.md,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                MetricCard.success(
                  title: 'Customer Satisfaction',
                  value: '98%',
                  subtitle: 'Based on 120 reviews',
                ),
                MetricCard.warning(
                  title: 'Budget Utilization',
                  value: '85%',
                  subtitle: 'Approaching limit',
                ),
                MetricCard.error(
                  title: 'Equipment Failures',
                  value: '2',
                  subtitle: 'Maintenance required',
                ),
                MetricCard.info(
                  title: 'Staff Attendance',
                  value: '92%',
                  subtitle: 'This month',
                ),
              ],
            ),
            AppSpacing.verticalSpaceLg,
            const Text(
              'Without Subtitles',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
            ),
            AppSpacing.verticalSpaceSm,
            GridView.count(
              crossAxisCount: 2,
              mainAxisSpacing: AppSpacing.md,
              crossAxisSpacing: AppSpacing.md,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                MetricCard.success(
                  title: 'Revenue',
                  value: 'Rp 12.5M',
                  icon: Icons.attach_money,
                ),
                MetricCard.warning(
                  title: 'Pending Orders',
                  value: '8',
                  icon: Icons.pending_actions,
                ),
                MetricCard.error(
                  title: 'Complaints',
                  value: '5',
                  icon: Icons.warning,
                ),
                MetricCard.info(
                  title: 'New Customers',
                  value: '24',
                  icon: Icons.person_add,
                ),
              ],
            ),
            AppSpacing.verticalSpaceLg,
            const Text(
              'Minimal',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
            ),
            AppSpacing.verticalSpaceSm,
            GridView.count(
              crossAxisCount: 2,
              mainAxisSpacing: AppSpacing.md,
              crossAxisSpacing: AppSpacing.md,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                MetricCard.success(title: 'Completed Tasks', value: '42'),
                MetricCard.warning(title: 'In Progress', value: '15'),
                MetricCard.error(title: 'Overdue', value: '7'),
                MetricCard.info(title: 'Total Tasks', value: '64'),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
