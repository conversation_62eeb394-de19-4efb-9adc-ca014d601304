import '../models/dashboard_configuration_model.dart';

/// Validator untuk memvalidasi integritas konfigurasi dashboard
///
/// Validator ini memastikan bahwa:
/// - Se<PERSON>a komponen yang dikonfigurasi valid
/// - Layout sesuai dengan komponen yang tersedia
/// - Navigasi konsisten dengan permission yang dip<PERSON>lukan
/// - Tidak ada konflik antar konfigurasi
class DashboardConfigurationValidator {
  static const List<String> validRoles = [
    'admin_yayasan',
    'perwakilan_yayasan',
    'kepala_dapur',
    'kepala_sppg', // Alternative name for kepala_dapur
    'ahli_gizi',
    'akuntan',
    'pengawas_pemeliharaan',
    'pengawas_pemeliharaan_penghantaran', // Full name as per MBG documentation
  ];

  /// Memvalidasi konfigurasi dashboard secara menyeluruh
  static ValidationResult validate(DashboardConfigurationModel config) {
    final errors = <String>[];
    final warnings = <String>[];

    // Validasi role
    _validateRole(config.roleId, errors);

    // Validasi komponen
    _validateComponents(config.components, errors, warnings);

    // Validasi layout
    _validateLayout(config.layout, config.components, errors, warnings);

    // Validasi navigasi
    _validateNavigation(config.navigation, errors, warnings);

    // Validasi konsistensi antar konfigurasi
    _validateConsistency(config, errors, warnings);

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Memvalidasi role yang dikonfigurasi
  static void _validateRole(String roleId, List<String> errors) {
    if (roleId.isEmpty) {
      errors.add('Role ID tidak boleh kosong');
      return;
    }

    if (!validRoles.contains(roleId)) {
      errors.add(
        'Role ID tidak valid: $roleId. Role yang valid: ${validRoles.join(', ')}',
      );
    }
  }

  /// Memvalidasi daftar komponen
  static void _validateComponents(
    List<ComponentConfigModel> components,
    List<String> errors,
    List<String> warnings,
  ) {
    if (components.isEmpty) {
      warnings.add('Tidak ada komponen yang dikonfigurasi');
      return;
    }

    final componentIds = <String>{};

    for (final component in components) {
      // Validasi duplicate ID
      if (componentIds.contains(component.componentId)) {
        errors.add(
          'Duplicate component ID ditemukan: ${component.componentId}',
        );
      }
      componentIds.add(component.componentId);

      // Validasi konfigurasi komponen
      _validateComponentConfig(component, errors, warnings);
    }
  }

  /// Memvalidasi konfigurasi individual komponen
  static void _validateComponentConfig(
    ComponentConfigModel component,
    List<String> errors,
    List<String> warnings,
  ) {
    // Validasi posisi grid
    final pos = component.position;
    if (pos.column < 0 || pos.row < 0) {
      errors.add(
        'Posisi grid tidak valid untuk komponen ${component.componentId}: column=${pos.column}, row=${pos.row}',
      );
    }
    if (pos.columnSpan <= 0 || pos.rowSpan <= 0) {
      errors.add(
        'Ukuran grid tidak valid untuk komponen ${component.componentId}: columnSpan=${pos.columnSpan}, rowSpan=${pos.rowSpan}',
      );
    }

    // Validasi permission
    if (component.requiredPermissions.isEmpty) {
      warnings.add(
        'Komponen ${component.componentId} tidak memiliki permission yang diperlukan',
      );
    }

    // Validasi parameters
    if (component.parameters.isEmpty) {
      warnings.add(
        'Komponen ${component.componentId} tidak memiliki parameter yang dikonfigurasi',
      );
    }

    // Validasi refresh interval
    if (component.autoRefresh && component.refreshIntervalSeconds <= 0) {
      errors.add(
        'Komponen ${component.componentId} memiliki interval refresh yang tidak valid: ${component.refreshIntervalSeconds}',
      );
    }

    // Validasi enabled state
    if (!component.enabled) {
      warnings.add('Komponen ${component.componentId} saat ini dinonaktifkan');
    }
  }

  /// Memvalidasi konfigurasi layout
  static void _validateLayout(
    LayoutConfigurationModel layout,
    List<ComponentConfigModel> components,
    List<String> errors,
    List<String> warnings,
  ) {
    // Validasi basic layout properties
    if (layout.desktopColumns <= 0) {
      errors.add('Desktop columns harus lebih dari 0');
    }
    if (layout.tabletColumns <= 0) {
      errors.add('Tablet columns harus lebih dari 0');
    }
    if (layout.mobileColumns <= 0) {
      errors.add('Mobile columns harus lebih dari 0');
    }

    // Validasi spacing
    if (layout.spacing < 0) {
      errors.add('Layout spacing tidak boleh negatif');
    }

    // Validasi breakpoints
    if (layout.responsiveBreakpoints != null) {
      _validateResponsiveBreakpoints(
        layout.responsiveBreakpoints!,
        errors,
        warnings,
      );
    }

    // Validasi komponen dalam grid
    _validateComponentsInGrid(components, layout.desktopColumns, errors);
  }

  /// Validasi komponen dalam batas grid
  static void _validateComponentsInGrid(
    List<ComponentConfigModel> components,
    int maxColumns,
    List<String> errors,
  ) {
    for (final component in components) {
      final pos = component.position;
      if (pos.column + pos.columnSpan > maxColumns) {
        errors.add(
          'Komponen ${component.componentId} melebihi batas grid columns',
        );
      }
    }

    // Validasi overlap sederhana
    for (int i = 0; i < components.length; i++) {
      for (int j = i + 1; j < components.length; j++) {
        final comp1 = components[i];
        final comp2 = components[j];

        // Check for grid overlap
        final c1Pos = comp1.position;
        final c2Pos = comp2.position;

        final c1EndCol = c1Pos.column + c1Pos.columnSpan;
        final c1EndRow = c1Pos.row + c1Pos.rowSpan;
        final c2EndCol = c2Pos.column + c2Pos.columnSpan;
        final c2EndRow = c2Pos.row + c2Pos.rowSpan;

        final overlaps =
            !(c1Pos.column >= c2EndCol ||
                c2Pos.column >= c1EndCol ||
                c1Pos.row >= c2EndRow ||
                c2Pos.row >= c1EndRow);

        if (overlaps) {
          errors.add(
            'Komponen ${comp1.componentId} dan ${comp2.componentId} memiliki posisi yang overlap',
          );
        }
      }
    }
  }

  /// Validasi responsive breakpoints
  static void _validateResponsiveBreakpoints(
    ResponsiveBreakpointsModel breakpoints,
    List<String> errors,
    List<String> warnings,
  ) {
    // Validasi nilai breakpoint
    if (breakpoints.tablet <= 0) {
      errors.add('Tablet breakpoint harus lebih dari 0');
    }
    if (breakpoints.desktop <= breakpoints.tablet) {
      errors.add('Desktop breakpoint harus lebih besar dari tablet breakpoint');
    }
  }

  /// Memvalidasi konfigurasi navigasi
  static void _validateNavigation(
    NavigationConfigurationModel navigation,
    List<String> errors,
    List<String> warnings,
  ) {
    if (navigation.sections.isEmpty) {
      warnings.add('Tidak ada navigation sections yang dikonfigurasi');
    }

    for (final section in navigation.sections) {
      _validateNavigationSection(section, errors, warnings);
    }

    // Validasi settings
    if (navigation.isCollapsible && navigation.defaultCollapsed) {
      warnings.add(
        'Navigation dikonfigurasi sebagai collapsible dan default collapsed',
      );
    }

    // Validasi width
    if (navigation.expandedWidth <= navigation.collapsedWidth) {
      errors.add('Expanded width harus lebih besar dari collapsed width');
    }
  }

  /// Validasi navigation section
  static void _validateNavigationSection(
    NavigationSectionModel section,
    List<String> errors,
    List<String> warnings,
  ) {
    if (section.title.isEmpty) {
      errors.add('Navigation section harus memiliki title');
    }

    if (section.items.isEmpty) {
      warnings.add(
        'Navigation section "${section.title}" tidak memiliki items',
      );
    }

    final itemRoutes = <String>{};
    for (final item in section.items) {
      // Validasi duplicate route (instead of ID)
      if (itemRoutes.contains(item.route)) {
        errors.add(
          'Duplicate navigation item route: ${item.route} dalam section "${section.title}"',
        );
      }
      itemRoutes.add(item.route);

      // Validasi item properties
      if (item.route.isEmpty) {
        errors.add('Navigation item "${item.title}" membutuhkan route');
      }

      if (item.title.isEmpty) {
        errors.add(
          'Navigation item dengan route ${item.route} membutuhkan title',
        );
      }
    }
  }

  /// Memvalidasi konsistensi antar konfigurasi
  static void _validateConsistency(
    DashboardConfigurationModel config,
    List<String> errors,
    List<String> warnings,
  ) {
    // Validasi konsistensi permission antara komponen dan navigasi
    final allComponentPermissions = <String>{};
    for (final component in config.components) {
      allComponentPermissions.addAll(component.requiredPermissions);
    }

    final allNavigationPermissions = <String>{};
    for (final section in config.navigation.sections) {
      allNavigationPermissions.addAll(section.requiredPermissions);
      for (final item in section.items) {
        allNavigationPermissions.addAll(item.requiredPermissions);
      }
    }

    // Cek apakah ada permission yang hanya ada di komponen tapi tidak di navigasi
    for (final permission in allComponentPermissions) {
      if (!allNavigationPermissions.contains(permission)) {
        warnings.add(
          'Permission $permission digunakan di komponen tapi tidak di navigasi',
        );
      }
    }

    // Validasi version compatibility
    if (config.configVersion.isEmpty) {
      warnings.add('Config version tidak ditentukan');
    }

    // Validasi timestamp consistency
    if (config.createdAt != null && config.updatedAt != null) {
      if (config.updatedAt!.isBefore(config.createdAt!)) {
        errors.add(
          'Updated timestamp tidak boleh lebih awal dari created timestamp',
        );
      }
    }
  }
}

/// Hasil validasi konfigurasi
class ValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  const ValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
  });

  /// Menghasilkan summary dari hasil validasi
  String get summary {
    if (isValid && warnings.isEmpty) {
      return 'Konfigurasi valid tanpa masalah';
    }

    final buffer = StringBuffer();

    if (!isValid) {
      buffer.writeln('❌ Konfigurasi tidak valid:');
      for (final error in errors) {
        buffer.writeln('  • $error');
      }
    }

    if (warnings.isNotEmpty) {
      if (!isValid) buffer.writeln();
      buffer.writeln('⚠️  Peringatan:');
      for (final warning in warnings) {
        buffer.writeln('  • $warning');
      }
    }

    return buffer.toString().trim();
  }

  @override
  String toString() => summary;
}
