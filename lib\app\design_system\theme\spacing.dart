import 'package:flutter/material.dart';

/// Consistent spacing system for SOD-MBG application
/// Provides standardized spacing values for layouts and components
class AppSpacing {
  AppSpacing._();

  /// Base spacing unit (8px) - follows Material Design 8dp grid
  static const double _baseUnit = 8.0;

  // Spacing constants
  static const double xs = _baseUnit * 0.5; // 4px
  static const double sm = _baseUnit * 1.0; // 8px
  static const double md = _baseUnit * 2.0; // 16px
  static const double lg = _baseUnit * 3.0; // 24px
  static const double xl = _baseUnit * 4.0; // 32px
  static const double xxl = _baseUnit * 6.0; // 48px
  static const double xxxl = _baseUnit * 8.0; // 64px

  // Component-specific spacing
  static const double cardPadding = md; // 16px
  static const double cardMargin = sm; // 8px
  static const double buttonPadding = md; // 16px
  static const double formFieldSpacing = md; // 16px
  static const double sectionSpacing = lg; // 24px
  static const double pageMargin = md; // 16px

  // Layout spacing
  static const double screenPadding = md; // 16px
  static const double contentSpacing = lg; // 24px
  static const double listItemSpacing = sm; // 8px
  static const double gridSpacing = md; // 16px

  // Navigation spacing
  static const double navigationPadding = sm; // 8px
  static const double navigationItemSpacing = xs; // 4px
  static const double appBarPadding = md; // 16px

  /// EdgeInsets presets for common use cases

  // All sides
  static const EdgeInsets allXs = EdgeInsets.all(xs);
  static const EdgeInsets allSm = EdgeInsets.all(sm);
  static const EdgeInsets allMd = EdgeInsets.all(md);
  static const EdgeInsets allLg = EdgeInsets.all(lg);
  static const EdgeInsets allXl = EdgeInsets.all(xl);

  // Horizontal only
  static const EdgeInsets horizontalXs = EdgeInsets.symmetric(horizontal: xs);
  static const EdgeInsets horizontalSm = EdgeInsets.symmetric(horizontal: sm);
  static const EdgeInsets horizontalMd = EdgeInsets.symmetric(horizontal: md);
  static const EdgeInsets horizontalLg = EdgeInsets.symmetric(horizontal: lg);
  static const EdgeInsets horizontalXl = EdgeInsets.symmetric(horizontal: xl);

  // Vertical only
  static const EdgeInsets verticalXs = EdgeInsets.symmetric(vertical: xs);
  static const EdgeInsets verticalSm = EdgeInsets.symmetric(vertical: sm);
  static const EdgeInsets verticalMd = EdgeInsets.symmetric(vertical: md);
  static const EdgeInsets verticalLg = EdgeInsets.symmetric(vertical: lg);
  static const EdgeInsets verticalXl = EdgeInsets.symmetric(vertical: xl);

  // Specific component padding
  static const EdgeInsets cardPaddingInsets = EdgeInsets.all(cardPadding);
  static const EdgeInsets buttonPaddingInsets = EdgeInsets.symmetric(
    horizontal: buttonPadding,
    vertical: sm,
  );
  static const EdgeInsets screenPaddingInsets = EdgeInsets.all(screenPadding);
  static const EdgeInsets formFieldPaddingInsets = EdgeInsets.all(
    formFieldSpacing,
  );

  // Top only
  static const EdgeInsets topXs = EdgeInsets.only(top: xs);
  static const EdgeInsets topSm = EdgeInsets.only(top: sm);
  static const EdgeInsets topMd = EdgeInsets.only(top: md);
  static const EdgeInsets topLg = EdgeInsets.only(top: lg);

  // Bottom only
  static const EdgeInsets bottomXs = EdgeInsets.only(bottom: xs);
  static const EdgeInsets bottomSm = EdgeInsets.only(bottom: sm);
  static const EdgeInsets bottomMd = EdgeInsets.only(bottom: md);
  static const EdgeInsets bottomLg = EdgeInsets.only(bottom: lg);

  // Left only
  static const EdgeInsets leftXs = EdgeInsets.only(left: xs);
  static const EdgeInsets leftSm = EdgeInsets.only(left: sm);
  static const EdgeInsets leftMd = EdgeInsets.only(left: md);
  static const EdgeInsets leftLg = EdgeInsets.only(left: lg);

  // Right only
  static const EdgeInsets rightXs = EdgeInsets.only(right: xs);
  static const EdgeInsets rightSm = EdgeInsets.only(right: sm);
  static const EdgeInsets rightMd = EdgeInsets.only(right: md);
  static const EdgeInsets rightLg = EdgeInsets.only(right: lg);

  /// SizedBox presets for spacing between widgets

  // Vertical spacing
  static const Widget verticalSpaceXs = SizedBox(height: xs);
  static const Widget verticalSpaceSm = SizedBox(height: sm);
  static const Widget verticalSpaceMd = SizedBox(height: md);
  static const Widget verticalSpaceLg = SizedBox(height: lg);
  static const Widget verticalSpaceXl = SizedBox(height: xl);
  static const Widget verticalSpaceXxl = SizedBox(height: xxl);

  // Horizontal spacing
  static const Widget horizontalSpaceXs = SizedBox(width: xs);
  static const Widget horizontalSpaceSm = SizedBox(width: sm);
  static const Widget horizontalSpaceMd = SizedBox(width: md);
  static const Widget horizontalSpaceLg = SizedBox(width: lg);
  static const Widget horizontalSpaceXl = SizedBox(width: xl);
  static const Widget horizontalSpaceXxl = SizedBox(width: xxl);

  /// Utility methods for dynamic spacing

  /// Get spacing value by multiplier
  static double getSpacing(double multiplier) {
    return _baseUnit * multiplier;
  }

  /// Get responsive spacing based on screen width
  static double getResponsiveSpacing(double baseSpacing, double screenWidth) {
    if (screenWidth < 600) {
      // Mobile - reduce spacing by 25%
      return baseSpacing * 0.75;
    } else if (screenWidth < 1024) {
      // Tablet - normal spacing
      return baseSpacing;
    } else {
      // Desktop - increase spacing by 25%
      return baseSpacing * 1.25;
    }
  }

  /// Get responsive padding based on screen width
  static EdgeInsets getResponsivePadding(
    EdgeInsets basePadding,
    double screenWidth,
  ) {
    final multiplier = getResponsiveMultiplier(screenWidth);
    return EdgeInsets.only(
      left: basePadding.left * multiplier,
      top: basePadding.top * multiplier,
      right: basePadding.right * multiplier,
      bottom: basePadding.bottom * multiplier,
    );
  }

  /// Get responsive multiplier based on screen width
  static double getResponsiveMultiplier(double screenWidth) {
    if (screenWidth < 600) {
      return 0.75; // Mobile - 25% smaller
    } else if (screenWidth < 1024) {
      return 1.0; // Tablet - normal
    } else {
      return 1.25; // Desktop - 25% larger
    }
  }

  /// Create custom EdgeInsets with responsive values
  static EdgeInsets responsive({
    required double screenWidth,
    double? all,
    double? horizontal,
    double? vertical,
    double? left,
    double? top,
    double? right,
    double? bottom,
  }) {
    final multiplier = getResponsiveMultiplier(screenWidth);

    if (all != null) {
      return EdgeInsets.all(all * multiplier);
    }

    if (horizontal != null || vertical != null) {
      return EdgeInsets.symmetric(
        horizontal: (horizontal ?? 0) * multiplier,
        vertical: (vertical ?? 0) * multiplier,
      );
    }

    return EdgeInsets.only(
      left: (left ?? 0) * multiplier,
      top: (top ?? 0) * multiplier,
      right: (right ?? 0) * multiplier,
      bottom: (bottom ?? 0) * multiplier,
    );
  }

  /// Create SizedBox with responsive spacing
  static Widget responsiveVerticalSpace(double height, double screenWidth) {
    return SizedBox(height: height * getResponsiveMultiplier(screenWidth));
  }

  static Widget responsiveHorizontalSpace(double width, double screenWidth) {
    return SizedBox(width: width * getResponsiveMultiplier(screenWidth));
  }
}
