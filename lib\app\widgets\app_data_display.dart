import 'package:fluent_ui/fluent_ui.dart';
import 'package:logger/logger.dart';
import '../constants/app_colors.dart';
import '../constants/app_typography.dart';
import '../constants/app_spacing.dart';
import '../constants/app_radius.dart';

/// Factory for creating consistent data display components with Fluent UI
/// Following the project's pattern of using factory methods for UI consistency
class AppDataDisplay {
  static final Logger _logger = Logger();

  /// Creates a simple data table with Fluent UI styling
  static Widget simpleDataTable({
    required List<String> headers,
    required List<List<String>> rows,
    EdgeInsets? padding,
    double? columnSpacing,
    double? rowHeight,
  }) {
    _logger.d('Creating simple data table with ${headers.length} columns and ${rows.length} rows');
    
    return Container(
      padding: padding ?? const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColors.surfaceColor,
        borderRadius: BorderRadius.circular(AppRadius.card),
        border: Border.all(color: AppColors.borderPrimary),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header row
            Container(
              decoration: BoxDecoration(
                color: AppColors.backgroundSecondary,
                borderRadius: BorderRadius.circular(AppRadius.sm),
              ),
              child: Padding(
                padding: const EdgeInsets.all(AppSpacing.md),
                child: Row(
                  children: headers.map((header) => Expanded(
                    child: Text(
                      header,
                      style: AppTypography.labelMedium.copyWith(
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  )).toList(),
                ),
              ),
            ),
            const SizedBox(height: AppSpacing.sm),
            // Data rows
            ...rows.map((row) => Container(
              margin: const EdgeInsets.only(bottom: AppSpacing.xs),
              padding: const EdgeInsets.all(AppSpacing.md),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(AppRadius.sm),
                border: Border.all(color: AppColors.borderPrimary.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: row.map((cell) => Expanded(
                  child: Text(
                    cell,
                    style: AppTypography.bodyMedium.copyWith(
                      color: AppColors.textPrimary,
                    ),
                  ),
                )).toList(),
              ),
            )),
          ],
        ),
      ),
    );
  }

  /// Creates a list tile with Fluent UI styling
  static Widget listTile({
    Widget? leading,
    required Widget title,
    Widget? subtitle,
    Widget? trailing,
    VoidCallback? onTap,
    bool selected = false,
    bool enabled = true,
    EdgeInsets? padding,
    Color? backgroundColor,
  }) {
    _logger.d('Creating list tile: ${title.toString()}');
    
    return Container(
      padding: padding ?? const EdgeInsets.symmetric(
        horizontal: AppSpacing.md,
        vertical: AppSpacing.sm,
      ),
      decoration: BoxDecoration(
        color: backgroundColor ?? (selected ? AppColors.primaryLight.withValues(alpha: 0.1) : Colors.transparent),
        borderRadius: BorderRadius.circular(AppRadius.md),
      ),
      child: Button(
        onPressed: enabled ? onTap : null,
        style: ButtonStyle(
          backgroundColor: WidgetStateProperty.all(Colors.transparent),
          padding: WidgetStateProperty.all(EdgeInsets.zero),
        ),
        child: Row(
          children: [
            if (leading != null)
              Padding(
                padding: const EdgeInsets.only(right: AppSpacing.md),
                child: leading,
              ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  title,
                  if (subtitle != null)
                    subtitle,
                ],
              ),
            ),
            if (trailing != null)
              Padding(
                padding: const EdgeInsets.only(left: AppSpacing.md),
                child: trailing,
              ),
          ],
        ),
      ),
    );
  }

  /// Creates an info label with Fluent UI styling
  static Widget infoLabel({
    required String label,
    required Widget child,
    bool isRequired = false,
    EdgeInsets? padding,
    TextStyle? labelStyle,
  }) {
    _logger.d('Creating info label: $label');
    
    return Container(
      padding: padding ?? const EdgeInsets.symmetric(vertical: AppSpacing.xs),
      child: InfoLabel(
        label: isRequired ? '$label *' : label,
        labelStyle: labelStyle ?? AppTypography.labelMedium.copyWith(
          color: AppColors.textPrimary,
          fontWeight: FontWeight.w600,
        ),
        child: child,
      ),
    );
  }

  /// Creates a progress ring with Fluent UI styling
  static Widget progressRing({
    double? value,
    double strokeWidth = 4.0,
    Color? activeColor,
    Color? backgroundColor,
    double size = 40.0,
    EdgeInsets? padding,
  }) {
    _logger.d('Creating progress ring with value: $value');
    
    return Container(
      padding: padding ?? EdgeInsets.zero,
      child: SizedBox(
        width: size,
        height: size,
        child: ProgressRing(
          value: value,
          strokeWidth: strokeWidth,
          activeColor: activeColor ?? AppColors.primary,
          backgroundColor: backgroundColor ?? AppColors.neutralGray200,
        ),
      ),
    );
  }

  /// Creates a progress bar with Fluent UI styling
  static Widget progressBar({
    double? value,
    double strokeWidth = 4.0,
    Color? activeColor,
    Color? backgroundColor,
    EdgeInsets? padding,
    String? label,
  }) {
    _logger.d('Creating progress bar with value: $value');
    
    return Container(
      padding: padding ?? const EdgeInsets.all(AppSpacing.sm),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (label != null)
            Padding(
              padding: const EdgeInsets.only(bottom: AppSpacing.xs),
              child: Text(
                label,
                style: AppTypography.labelMedium.copyWith(
                  color: AppColors.textPrimary,
                ),
              ),
            ),
          ProgressBar(
            value: value,
            strokeWidth: strokeWidth,
            activeColor: activeColor ?? AppColors.primary,
            backgroundColor: backgroundColor ?? AppColors.neutralGray200,
          ),
        ],
      ),
    );
  }

  /// Creates an expander with Fluent UI styling
  static Widget expander({
    required Widget header,
    required Widget content,
    bool initiallyExpanded = false,
    ValueChanged<bool>? onExpansionChanged,
    EdgeInsets? padding,
    Color? backgroundColor,
  }) {
    _logger.d('Creating expander: ${header.toString()}');
    
    return Container(
      padding: padding ?? const EdgeInsets.all(AppSpacing.sm),
      child: Expander(
        header: header,
        content: content,
        initiallyExpanded: initiallyExpanded,
        onStateChanged: onExpansionChanged,
        headerBackgroundColor: WidgetStateProperty.all(backgroundColor ?? AppColors.backgroundSecondary),
      ),
    );
  }

  /// Creates a tree view with Fluent UI styling
  static Widget treeView({
    required List<TreeViewItem> items,
    bool selectionMode = false,
    EdgeInsets? padding,
  }) {
    _logger.d('Creating tree view with ${items.length} items');
    
    return Container(
      padding: padding ?? const EdgeInsets.all(AppSpacing.md),
      child: TreeView(
        items: items,
        selectionMode: selectionMode ? TreeViewSelectionMode.multiple : TreeViewSelectionMode.none,
      ),
    );
  }

  /// Creates a tree view item
  static TreeViewItem treeViewItem({
    required Widget content,
    List<TreeViewItem>? children,
    bool expanded = false,
    Widget? leading,
  }) {
    _logger.d('Creating tree view item: ${content.toString()}');
    
    return TreeViewItem(
      content: content,
      children: children ?? [],
      expanded: expanded,
      leading: leading,
    );
  }

  /// Creates an info badge with Fluent UI styling
  static Widget infoBadge({
    required int value,
    Color? backgroundColor,
    Color? textColor,
    EdgeInsets? padding,
  }) {
    _logger.d('Creating info badge with value: $value');
    
    return Container(
      padding: padding ?? EdgeInsets.zero,
      child: InfoBadge(
        source: Text(
          value.toString(),
          style: AppTypography.bodySmall.copyWith(
            color: textColor ?? AppColors.textOnPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        color: backgroundColor ?? AppColors.primary,
      ),
    );
  }

  /// Creates a rating bar with Fluent UI styling
  static Widget ratingBar({
    required double rating,
    int maxRating = 5,
    ValueChanged<double>? onRatingChanged,
    double size = 24.0,
    Color? activeColor,
    EdgeInsets? padding,
    bool readOnly = false,
  }) {
    _logger.d('Creating rating bar with rating: $rating');
    
    return Container(
      padding: padding ?? EdgeInsets.zero,
      child: RatingBar(
        rating: rating,
        amount: maxRating,
        onChanged: readOnly ? null : onRatingChanged,
        iconSize: size,
        starSpacing: AppSpacing.xs,
        icon: FluentIcons.favorite_star,
      ),
    );
  }

  /// Creates a kitchen-specific data display card
  static Widget kitchenDataCard({
    required String title,
    required String value,
    required String unit,
    IconData? icon,
    Color? valueColor,
    Color? backgroundColor,
    EdgeInsets? padding,
    VoidCallback? onTap,
  }) {
    _logger.d('Creating kitchen data card: $title');
    
    return Container(
      padding: padding ?? const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.surfaceColor,
        borderRadius: BorderRadius.circular(AppRadius.card),
        border: Border.all(
          color: AppColors.borderPrimary,
          width: 1,
        ),
      ),
      child: Button(
        onPressed: onTap,
        style: ButtonStyle(
          backgroundColor: WidgetStateProperty.all(Colors.transparent),
          padding: WidgetStateProperty.all(const EdgeInsets.all(AppSpacing.md)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                if (icon != null)
                  Icon(
                    icon,
                    color: AppColors.primary,
                    size: 24,
                  ),
                if (icon != null)
                  const SizedBox(width: AppSpacing.sm),
                Expanded(
                  child: Text(
                    title,
                    style: AppTypography.labelMedium.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppSpacing.sm),
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  value,
                  style: AppTypography.h3.copyWith(
                    color: valueColor ?? AppColors.textPrimary,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                const SizedBox(width: AppSpacing.xs),
                Text(
                  unit,
                  style: AppTypography.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Creates a status indicator with Fluent UI styling
  static Widget statusIndicator({
    required String status,
    required String label,
    Color? statusColor,
    IconData? icon,
    EdgeInsets? padding,
  }) {
    _logger.d('Creating status indicator: $status');
    
    Color indicatorColor = statusColor ?? AppColors.getSemanticColor(status);
    
    return Container(
      padding: padding ?? const EdgeInsets.all(AppSpacing.sm),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: indicatorColor,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: AppSpacing.sm),
          if (icon != null)
            Icon(
              icon,
              color: indicatorColor,
              size: 16,
            ),
          if (icon != null)
            const SizedBox(width: AppSpacing.xs),
          Text(
            label,
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  /// Creates a kitchen metric display
  static Widget kitchenMetrics({
    required List<Map<String, dynamic>> metrics,
    EdgeInsets? padding,
    int columns = 2,
  }) {
    _logger.d('Creating kitchen metrics with ${metrics.length} items');
    
    return Container(
      padding: padding ?? const EdgeInsets.all(AppSpacing.md),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: columns,
          crossAxisSpacing: AppSpacing.md,
          mainAxisSpacing: AppSpacing.md,
          childAspectRatio: 1.5,
        ),
        itemCount: metrics.length,
        itemBuilder: (context, index) {
          final metric = metrics[index];
          return kitchenDataCard(
            title: metric['title'] as String,
            value: metric['value'] as String,
            unit: metric['unit'] as String,
            icon: metric['icon'] as IconData?,
            valueColor: metric['valueColor'] as Color?,
            onTap: metric['onTap'] as VoidCallback?,
          );
        },
      ),
    );
  }
}
