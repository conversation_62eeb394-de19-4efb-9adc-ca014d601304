# ResponsiveAppBar

ResponsiveAppBar adalah komponen navigasi utama yang responsif dan berbasis peran untuk aplikasi SOD-MBG. Komponen ini menyesuaikan tampilan berdasarkan ukuran layar dan hanya menampilkan item navigasi yang sesuai dengan peran pengguna.

## Fitur

- Responsif terhadap berbagai ukuran layar (desktop, tablet, mobile)
- Integrasi dengan sistem permission untuk menampilkan navigasi berbasis peran
- Dukungan untuk ikon dan label pada item navigasi
- Styling yang konsisten dengan Material Design 3
- Dukungan untuk custom title widget dan bottom widget (seperti TabBar)

## Penggunaan

```dart
ResponsiveAppBar(
  title: 'SOD-MBG',
  centerTitle: false,
  selectedIndex: _selectedIndex,
  onNavigationItemSelected: (index) {
    setState(() {
      _selectedIndex = index;
    });
  },
  navigationItems: const [
    ResponsiveAppBarItem(
      label: 'Dashboard',
      icon: Icons.dashboard,
      permissionType: AdminPermission,
    ),
    ResponsiveAppBarItem(
      label: 'Operasi Dapur',
      icon: Icons.restaurant,
      permissionType: KitchenManagementPermission,
    ),
    // More navigation items...
  ],
  actions: [
    IconButton(
      icon: const Icon(Icons.notifications),
      onPressed: () {},
    ),
    IconButton(
      icon: const Icon(Icons.account_circle),
      onPressed: () {},
    ),
  ],
)
```

## Properti

| Properti | Tipe | Deskripsi |
|----------|------|-----------|
| `title` | `String` | Judul yang ditampilkan di AppBar |
| `actions` | `List<Widget>?` | Widget yang ditampilkan di sisi kanan AppBar |
| `leading` | `Widget?` | Widget yang ditampilkan di sisi kiri AppBar |
| `automaticallyImplyLeading` | `bool` | Apakah secara otomatis menampilkan tombol back |
| `navigationItems` | `List<ResponsiveAppBarItem>` | Item navigasi yang ditampilkan di AppBar |
| `onNavigationItemSelected` | `Function(int)?` | Callback saat item navigasi dipilih |
| `selectedIndex` | `int` | Indeks item navigasi yang dipilih |
| `centerTitle` | `bool` | Apakah judul ditampilkan di tengah |
| `titleWidget` | `Widget?` | Widget kustom untuk judul |
| `showNavigationItems` | `bool` | Apakah menampilkan item navigasi |
| `showTitle` | `bool` | Apakah menampilkan judul |
| `bottom` | `PreferredSizeWidget?` | Widget yang ditampilkan di bawah AppBar |

## ResponsiveAppBarItem

`ResponsiveAppBarItem` adalah model untuk item navigasi di ResponsiveAppBar.

### Properti

| Properti | Tipe | Deskripsi |
|----------|------|-----------|
| `label` | `String` | Label untuk item navigasi |
| `icon` | `IconData?` | Ikon untuk item navigasi |
| `permissionType` | `Type?` | Tipe permission yang diperlukan untuk melihat item ini |

## Integrasi dengan Sistem Permission

ResponsiveAppBar terintegrasi dengan sistem permission yang ada melalui extension `PermissionContextExtension` pada `BuildContext`. Item navigasi hanya akan ditampilkan jika pengguna memiliki permission yang sesuai.

Contoh permission yang didukung:
- `AdminPermission`
- `OversightPermission`
- `KitchenManagementPermission`
- `NutritionPermission`
- `FinancialPermission`
- `LogisticsPermission`

## Perilaku Responsif

- **Desktop** (≥1200px): Menampilkan semua item navigasi sebagai tombol teks dengan ikon di AppBar
- **Tablet** (600px-1199px): Menyembunyikan item navigasi dari AppBar (akan ditampilkan di sidebar atau drawer)
- **Mobile** (<600px): Menyembunyikan item navigasi dari AppBar (akan ditampilkan di bottom navigation atau drawer)

## Catatan Implementasi

ResponsiveAppBar menggunakan `AuthService` untuk memeriksa permission pengguna. Pastikan `AuthService` sudah diinisialisasi sebelum menggunakan ResponsiveAppBar dengan item navigasi berbasis permission.

```dart
@override
void initState() {
  super.initState();
  // Ensure AuthService is initialized
  _initializeAuthService();
}

Future<void> _initializeAuthService() async {
  if (!AuthService.instance.isInitialized) {
    await AuthService.instance.initialize();
  }
}
```

## Contoh Penggunaan

Lihat file `lib/app/design_system/examples/responsive_app_bar_example.dart` untuk contoh penggunaan lengkap.