import 'package:flutter/material.dart';
import 'app_sidebar.dart';
import '../../../../../core/permissions/permission_mixins.dart';
import '../../tokens/design_tokens.dart';
import '../../theme/spacing.dart';

/// Example implementation of AppSidebar with role-based menu items
class AppSidebarExample extends StatefulWidget {
  const AppSidebarExample({super.key});

  @override
  State<AppSidebarExample> createState() => _AppSidebarExampleState();
}

class _AppSidebarExampleState extends State<AppSidebarExample> {
  int _selectedIndex = 0;
  bool _isExpanded = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Row(
        children: [
          AppSidebar(
            selectedIndex: _selectedIndex,
            isExpanded: _isExpanded,
            onDestinationSelected: (index) {
              setState(() {
                _selectedIndex = index;
              });
            },
            onExpandButtonPressed: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
            header: _buildSidebarHeader(),
            trailing: _buildSidebarTrailing(),
            items: _getSidebarItems(),
          ),
          Expanded(
            child: Center(
              child: Text(
                'Selected item: ${_getSidebarItems()[_selectedIndex].label}',
                style: Theme.of(context).textTheme.headlineMedium,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the header widget for the sidebar
  Widget _buildSidebarHeader() {
    return Container(
      padding: EdgeInsets.all(AppSpacing.md),
      child: Column(
        crossAxisAlignment:
            _isExpanded ? CrossAxisAlignment.start : CrossAxisAlignment.center,
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: DesignTokens.primaryColors['green'],
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(Icons.restaurant, color: Colors.white),
          ),
          if (_isExpanded) ...[
            SizedBox(height: AppSpacing.sm),
            Text(
              'SOD-MBG',
              style: TextStyle(
                fontSize: DesignTokens.fontSizes['xl'],
                fontWeight: DesignTokens.fontWeights['bold'],
                color: DesignTokens.primaryColors['green'],
              ),
            ),
            Text(
              'Sistem Operasional Dapur',
              style: TextStyle(
                fontSize: DesignTokens.fontSizes['sm'],
                color: DesignTokens.neutralColors['grey-600'],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Builds the trailing widget for the sidebar
  Widget _buildSidebarTrailing() {
    return Container(
      padding: EdgeInsets.all(AppSpacing.md),
      child: Column(
        children: [
          const Divider(),
          ListTile(
            leading: const Icon(Icons.person),
            title: _isExpanded ? const Text('Profile') : null,
            contentPadding:
                _isExpanded
                    ? EdgeInsets.symmetric(horizontal: AppSpacing.sm)
                    : EdgeInsets.zero,
            minLeadingWidth: 0,
            horizontalTitleGap: AppSpacing.sm,
            onTap: () {},
          ),
          ListTile(
            leading: const Icon(Icons.logout),
            title: _isExpanded ? const Text('Logout') : null,
            contentPadding:
                _isExpanded
                    ? EdgeInsets.symmetric(horizontal: AppSpacing.sm)
                    : EdgeInsets.zero,
            minLeadingWidth: 0,
            horizontalTitleGap: AppSpacing.sm,
            onTap: () {},
          ),
        ],
      ),
    );
  }

  /// Returns the list of sidebar items with role-based permissions
  List<AppSidebarItem> _getSidebarItems() {
    return [
      // Dashboard - available to all roles
      const AppSidebarItem(icon: Icon(Icons.dashboard), label: 'Dashboard'),

      // Kitchen Operations - requires KitchenManagementPermission
      const AppSidebarItem(
        icon: Icon(Icons.restaurant),
        label: 'Kitchen Operations',
        requiredPermission: KitchenManagementPermission,
      ),

      // Nutrition Management - requires NutritionPermission
      const AppSidebarItem(
        icon: Icon(Icons.food_bank),
        label: 'Nutrition Management',
        requiredPermission: NutritionPermission,
      ),

      // Financial Management - requires FinancialPermission
      const AppSidebarItem(
        icon: Icon(Icons.attach_money),
        label: 'Financial Management',
        requiredPermission: FinancialPermission,
      ),

      // Logistics - requires LogisticsPermission
      const AppSidebarItem(
        icon: Icon(Icons.local_shipping),
        label: 'Logistics',
        requiredPermission: LogisticsPermission,
      ),

      // Divider
      const AppSidebarItem.divider(),

      // Admin Panel - requires AdminPermission
      const AppSidebarItem(
        icon: Icon(Icons.admin_panel_settings),
        label: 'Admin Panel',
        requiredPermission: AdminPermission,
      ),

      // Oversight - requires OversightPermission
      const AppSidebarItem(
        icon: Icon(Icons.visibility),
        label: 'Oversight',
        requiredPermission: OversightPermission,
      ),

      // Settings - available to all roles
      const AppSidebarItem(icon: Icon(Icons.settings), label: 'Settings'),

      // Help - available to all roles
      const AppSidebarItem(icon: Icon(Icons.help), label: 'Help'),
    ];
  }
}
