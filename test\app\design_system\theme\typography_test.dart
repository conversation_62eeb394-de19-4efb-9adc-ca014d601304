import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:aplikasi_sppg/app/design_system/theme/typography.dart';

void main() {
  group('AppTypography', () {
    test('should have consistent font family across all text styles', () {
      // Get all text styles from the text theme
      final textTheme = AppTypography.textTheme;
      final textStyles = [
        textTheme.displayLarge,
        textTheme.displayMedium,
        textTheme.displaySmall,
        textTheme.headlineLarge,
        textTheme.headlineMedium,
        textTheme.headlineSmall,
        textTheme.titleLarge,
        textTheme.titleMedium,
        textTheme.titleSmall,
        textTheme.bodyLarge,
        textTheme.bodyMedium,
        textTheme.bodySmall,
        textTheme.labelLarge,
        textTheme.labelMedium,
        textTheme.labelSmall,
      ];

      // Check that all text styles use the same font family
      for (final style in textStyles) {
        expect(style?.fontFamily, equals(AppTypography.fontFamily));
      }
    });

    test(
      'should have proper text style hierarchy with decreasing font sizes',
      () {
        final textTheme = AppTypography.textTheme;

        // Display styles should have decreasing sizes
        expect(
          textTheme.displayLarge?.fontSize,
          greaterThan(textTheme.displayMedium?.fontSize ?? 0),
        );
        expect(
          textTheme.displayMedium?.fontSize,
          greaterThan(textTheme.displaySmall?.fontSize ?? 0),
        );

        // Headline styles should have decreasing sizes
        expect(
          textTheme.headlineLarge?.fontSize,
          greaterThan(textTheme.headlineMedium?.fontSize ?? 0),
        );
        expect(
          textTheme.headlineMedium?.fontSize,
          greaterThan(textTheme.headlineSmall?.fontSize ?? 0),
        );

        // Title styles should have decreasing sizes
        expect(
          textTheme.titleLarge?.fontSize,
          greaterThan(textTheme.titleMedium?.fontSize ?? 0),
        );
        expect(
          textTheme.titleMedium?.fontSize,
          greaterThan(textTheme.titleSmall?.fontSize ?? 0),
        );

        // Body styles should have decreasing sizes
        expect(
          textTheme.bodyLarge?.fontSize,
          greaterThan(textTheme.bodyMedium?.fontSize ?? 0),
        );
        expect(
          textTheme.bodyMedium?.fontSize,
          greaterThan(textTheme.bodySmall?.fontSize ?? 0),
        );

        // Label styles should have decreasing sizes
        expect(
          textTheme.labelLarge?.fontSize,
          greaterThan(textTheme.labelMedium?.fontSize ?? 0),
        );
        expect(
          textTheme.labelMedium?.fontSize,
          greaterThan(textTheme.labelSmall?.fontSize ?? 0),
        );
      },
    );

    test('should have appropriate font weights for different text styles', () {
      final textTheme = AppTypography.textTheme;

      // Display styles should have normal weight
      expect(textTheme.displayLarge?.fontWeight, equals(FontWeight.w400));
      expect(textTheme.displayMedium?.fontWeight, equals(FontWeight.w400));
      expect(textTheme.displaySmall?.fontWeight, equals(FontWeight.w400));

      // Headline styles should have medium weight
      expect(textTheme.headlineLarge?.fontWeight, equals(FontWeight.w500));
      expect(textTheme.headlineMedium?.fontWeight, equals(FontWeight.w500));
      expect(textTheme.headlineSmall?.fontWeight, equals(FontWeight.w500));

      // Title styles should have medium weight
      expect(textTheme.titleLarge?.fontWeight, equals(FontWeight.w500));
      expect(textTheme.titleMedium?.fontWeight, equals(FontWeight.w500));
      expect(textTheme.titleSmall?.fontWeight, equals(FontWeight.w500));

      // Body styles should have normal weight
      expect(textTheme.bodyLarge?.fontWeight, equals(FontWeight.w400));
      expect(textTheme.bodyMedium?.fontWeight, equals(FontWeight.w400));
      expect(textTheme.bodySmall?.fontWeight, equals(FontWeight.w400));

      // Label styles should have medium weight
      expect(textTheme.labelLarge?.fontWeight, equals(FontWeight.w500));
      expect(textTheme.labelMedium?.fontWeight, equals(FontWeight.w500));
      expect(textTheme.labelSmall?.fontWeight, equals(FontWeight.w500));
    });

    test('should have custom text styles for specific use cases', () {
      // Check that custom text styles exist and have appropriate properties
      expect(AppTypography.metricValue.fontSize, equals(28));
      expect(AppTypography.metricValue.fontWeight, equals(FontWeight.w600));

      expect(AppTypography.metricLabel.fontSize, equals(12));
      expect(AppTypography.metricLabel.fontWeight, equals(FontWeight.w400));

      expect(AppTypography.navigationLabel.fontSize, equals(12));
      expect(AppTypography.navigationLabel.fontWeight, equals(FontWeight.w500));

      expect(AppTypography.fieldLabel.fontSize, equals(14));
      expect(AppTypography.fieldLabel.fontWeight, equals(FontWeight.w500));

      expect(AppTypography.errorMessage.fontSize, equals(12));
      expect(AppTypography.errorMessage.fontWeight, equals(FontWeight.w400));

      expect(AppTypography.helperMessage.fontSize, equals(12));
      expect(AppTypography.helperMessage.fontWeight, equals(FontWeight.w400));

      expect(AppTypography.buttonText.fontSize, equals(14));
      expect(AppTypography.buttonText.fontWeight, equals(FontWeight.w500));

      expect(AppTypography.snackbarText.fontSize, equals(14));
      expect(AppTypography.snackbarText.fontWeight, equals(FontWeight.w400));

      expect(AppTypography.dialogTitle.fontSize, equals(20));
      expect(AppTypography.dialogTitle.fontWeight, equals(FontWeight.w500));

      expect(AppTypography.dialogContent.fontSize, equals(14));
      expect(AppTypography.dialogContent.fontWeight, equals(FontWeight.w400));
    });

    test('should provide utility methods for text styling', () {
      final baseStyle = const TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: Colors.black,
      );

      // Test withColor utility
      final coloredStyle = AppTypography.withColor(baseStyle, Colors.red);
      expect(coloredStyle.color, equals(Colors.red));
      expect(coloredStyle.fontSize, equals(baseStyle.fontSize));
      expect(coloredStyle.fontWeight, equals(baseStyle.fontWeight));

      // Test withWeight utility
      final weightedStyle = AppTypography.withWeight(
        baseStyle,
        FontWeight.w700,
      );
      expect(weightedStyle.fontWeight, equals(FontWeight.w700));
      expect(weightedStyle.fontSize, equals(baseStyle.fontSize));
      expect(weightedStyle.color, equals(baseStyle.color));

      // Test withSize utility
      final sizedStyle = AppTypography.withSize(baseStyle, 18);
      expect(sizedStyle.fontSize, equals(18));
      expect(sizedStyle.fontWeight, equals(baseStyle.fontWeight));
      expect(sizedStyle.color, equals(baseStyle.color));

      // Test getResponsiveStyle utility
      final mobileStyle = AppTypography.getResponsiveStyle(baseStyle, 400);
      expect(mobileStyle.fontSize, equals(baseStyle.fontSize! * 0.9));

      final tabletStyle = AppTypography.getResponsiveStyle(baseStyle, 800);
      expect(tabletStyle.fontSize, equals(baseStyle.fontSize));

      final desktopStyle = AppTypography.getResponsiveStyle(baseStyle, 1200);
      expect(desktopStyle.fontSize, equals(baseStyle.fontSize! * 1.1));
    });
  });
}
