// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'navigation_configuration_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NavigationConfigurationModel _$NavigationConfigurationModelFromJson(
  Map<String, dynamic> json,
) => NavigationConfigurationModel(
  sections:
      (json['sections'] as List<dynamic>)
          .map(
            (e) => NavigationSectionModel.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
  isCollapsible: json['is_collapsible'] as bool? ?? true,
  defaultCollapsed: json['default_collapsed'] as bool? ?? false,
  expandedWidth: (json['expanded_width'] as num?)?.toDouble() ?? 240.0,
  collapsedWidth: (json['collapsed_width'] as num?)?.toDouble() ?? 56.0,
);

Map<String, dynamic> _$NavigationConfigurationModelToJson(
  NavigationConfigurationModel instance,
) => <String, dynamic>{
  'sections': instance.sections.map((e) => e.toJson()).toList(),
  'is_collapsible': instance.isCollapsible,
  'default_collapsed': instance.defaultCollapsed,
  'expanded_width': instance.expandedWidth,
  'collapsed_width': instance.collapsedWidth,
};

NavigationSectionModel _$NavigationSectionModelFromJson(
  Map<String, dynamic> json,
) => NavigationSectionModel(
  title: json['title'] as String,
  items:
      (json['items'] as List<dynamic>)
          .map((e) => NavigationItemModel.fromJson(e as Map<String, dynamic>))
          .toList(),
  requiredPermissions:
      (json['required_permissions'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      [],
  isCollapsible: json['is_collapsible'] as bool? ?? false,
  defaultExpanded: json['default_expanded'] as bool? ?? true,
);

Map<String, dynamic> _$NavigationSectionModelToJson(
  NavigationSectionModel instance,
) => <String, dynamic>{
  'title': instance.title,
  'items': instance.items.map((e) => e.toJson()).toList(),
  'required_permissions': instance.requiredPermissions,
  'is_collapsible': instance.isCollapsible,
  'default_expanded': instance.defaultExpanded,
};

NavigationItemModel _$NavigationItemModelFromJson(Map<String, dynamic> json) =>
    NavigationItemModel(
      title: json['title'] as String,
      route: json['route'] as String,
      iconCodePoint: (json['icon_code_point'] as num?)?.toInt(),
      iconFontFamily: json['icon_font_family'] as String?,
      requiredPermissions:
          (json['required_permissions'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      badgeCount: (json['badge_count'] as num?)?.toInt(),
      badgeColorHex: json['badge_color_hex'] as String?,
    );

Map<String, dynamic> _$NavigationItemModelToJson(
  NavigationItemModel instance,
) => <String, dynamic>{
  'title': instance.title,
  'route': instance.route,
  'icon_code_point': instance.iconCodePoint,
  'icon_font_family': instance.iconFontFamily,
  'required_permissions': instance.requiredPermissions,
  'badge_count': instance.badgeCount,
  'badge_color_hex': instance.badgeColorHex,
};
