import 'package:fluent_ui/fluent_ui.dart';
import 'package:logger/logger.dart';
import '../constants/app_icons.dart';
import '../constants/app_colors.dart';
import '../constants/app_typography.dart';
import '../constants/app_spacing.dart';
import '../constants/app_radius.dart';
import 'admin_yayasan_menu_structure.dart';

/// Navigation Pane khusus untuk SOD-MBG dengan Fluent UI
/// Menampilkan menu navigasi berdasarkan role user dengan design YellowBlueSkyHappy
class AppNavigationPane extends StatefulWidget {
  final String? userName;
  final String? userRole;
  final String? sppgName;
  final String? userAvatarUrl;
  final VoidCallback? onProfileTap;
  final VoidCallback? onSettingsTap;
  final VoidCallback? onLogoutTap;
  final int? selectedIndex;
  final ValueChanged<int>? onItemSelected;
  final bool isCompact;

  const AppNavigationPane({
    super.key,
    this.userName,
    this.userRole,
    this.sppgName,
    this.userAvatarUrl,
    this.onProfileTap,
    this.onSettingsTap,
    this.onLogoutTap,
    this.selectedIndex,
    this.onItemSelected,
    this.isCompact = false,
  });

  @override
  State<AppNavigationPane> createState() => _AppNavigationPaneState();
}

class _AppNavigationPaneState extends State<AppNavigationPane> {
  static final Logger _logger = Logger();
  
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.selectedIndex ?? 0;
    _logger.d('AppNavigationPane initialized with role: ${widget.userRole}');
  }

  @override
  Widget build(BuildContext context) {
    return NavigationView(
      appBar: NavigationAppBar(
        automaticallyImplyLeading: false,
        title: _buildHeader(),
        actions: Row(children: _buildAppBarActions()),
      ),
      pane: NavigationPane(
        size: widget.isCompact 
            ? const NavigationPaneSize(openWidth: 280, compactWidth: 50) 
            : const NavigationPaneSize(openWidth: 320, compactWidth: 50),
        header: _buildPaneHeader(),
        displayMode: widget.isCompact 
            ? PaneDisplayMode.compact 
            : PaneDisplayMode.open,
        selected: _currentIndex,
        onChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
          widget.onItemSelected?.call(index);
        },
        items: _buildNavigationItems(),
        footerItems: _buildFooterItems(),
      ),
    );
  }

  /// Build header with app title and user info
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
      child: Row(
        children: [
          // App Logo/Icon
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(AppRadius.sm),
            ),
            child: const Icon(
              AppIcons.localDining,
              color: AppColors.textOnPrimary,
              size: 18,
            ),
          ),
          const SizedBox(width: AppSpacing.sm),
          
          // App Title
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'SOD-MBG',
                  style: AppTypography.h5.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (widget.sppgName != null) ...[
                  Text(
                    widget.sppgName!,
                    style: AppTypography.caption.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build app bar actions
  List<Widget> _buildAppBarActions() {
    return [
      // User Profile Button
      if (widget.userName != null) ...[
        Button(
          onPressed: widget.onProfileTap,
          style: ButtonStyle(
            backgroundColor: WidgetStateProperty.all(Colors.transparent),
            padding: WidgetStateProperty.all(
              const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // User Avatar
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(12),
                ),
                    child: widget.userAvatarUrl != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(12),
                            child: Image.network(
                              widget.userAvatarUrl!,
                              width: 24,
                              height: 24,
                              fit: BoxFit.cover,
                            ),
                          )
                        : const Icon(
                            FluentIcons.contact,
                            color: AppColors.textOnPrimary,
                            size: 14,
                          ),
              ),
              const SizedBox(width: AppSpacing.xs),
              
              // User Name
              Text(
                widget.userName!,
                style: AppTypography.bodyMedium.copyWith(
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
        ),
      ],
      
      // Settings Button
      IconButton(
        onPressed: widget.onSettingsTap,
        icon: const Icon(FluentIcons.settings, size: 18),
        style: ButtonStyle(
          backgroundColor: WidgetStateProperty.all(Colors.transparent),
        ),
      ),
      
      const SizedBox(width: AppSpacing.xs),
    ];
  }

  /// Build pane header
  Widget? _buildPaneHeader() {
    if (widget.isCompact) return null;
    
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Role Badge
          if (widget.userRole != null) ...[
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppSpacing.sm,
                vertical: AppSpacing.xs,
              ),
              decoration: BoxDecoration(
                color: _getRoleColor(widget.userRole!).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppRadius.sm),
                border: Border.all(
                  color: _getRoleColor(widget.userRole!),
                  width: 1,
                ),
              ),
              child:              Text(
                _getRoleDisplayName(widget.userRole!),
                style: AppTypography.labelSmall.copyWith(
                  color: _getRoleColor(widget.userRole!),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const SizedBox(height: AppSpacing.md),
          ],
          
          // Quick Stats (placeholder)
          _buildQuickStats(),
        ],
      ),
    );
  }

  /// Build quick stats widget
  Widget _buildQuickStats() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.sm),
      decoration: BoxDecoration(
        color: AppColors.backgroundSecondary,
        borderRadius: BorderRadius.circular(AppRadius.md),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Status Hari Ini',
            style: AppTypography.labelMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: AppSpacing.xs),
          Row(
            children: [
              Icon(
                FluentIcons.check_mark,
                size: 16,
                color: AppColors.successGreen,
              ),
              const SizedBox(width: AppSpacing.xs),
              Text(
                'Operasional Normal',
                style: AppTypography.labelSmall.copyWith(
                  color: AppColors.successGreen,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build navigation items based on user role
  List<NavigationPaneItem> _buildNavigationItems() {
    final items = <NavigationPaneItem>[];
    
    switch (widget.userRole) {
      case 'admin_yayasan':
        items.addAll(_getAdminYayasanItems());
        break;
      case 'perwakilan_yayasan':
        items.addAll(_getPerwakilanYayasanItems());
        break;
      case 'kepala_dapur':
        items.addAll(_getKepalaDapurItems());
        break;
      case 'ahli_gizi':
        items.addAll(_getAhliGiziItems());
        break;
      case 'akuntan':
        items.addAll(_getAkuntanItems());
        break;
      case 'pengawas_logistik':
        items.addAll(_getPengawasLogistikItems());
        break;
      default:
        items.addAll(_getDefaultItems());
    }
    
    return items;
  }

  /// Build footer items
  List<NavigationPaneItem> _buildFooterItems() {
    final footerItems = <NavigationPaneItem>[];
    
    // Add role-specific footer if Admin Yayasan
    if (widget.userRole == 'admin_yayasan') {
      final adminFooterItems = AdminYayasanMenuStructure.getFooterItems();
      
      footerItems.addAll(
        adminFooterItems.map((menuItem) {
          return menuItem.toNavigationPaneItem(
            onRouteNavigate: (route) {
              _logger.d('Navigate to route: $route');
              if (route == '/profile') {
                widget.onProfileTap?.call();
              }
              // TODO: Implement navigation using AppRouter
            },
            onActionExecute: (action) {
              _logger.d('Execute action: $action');
              switch (action) {
                case 'help':
                  // Handle help action
                  break;
                case 'logout':
                  widget.onLogoutTap?.call();
                  break;
              }
            },
          );
        }).toList(),
      );
    } else {
      // Standard footer items for other roles
      footerItems.addAll([
        // Account Section for other roles
        PaneItemExpander(
          icon: const Icon(FluentIcons.contact),
          title: const Text('Akun Saya'),
          body: const SizedBox.shrink(),
          items: [
            PaneItem(
              icon: const Icon(FluentIcons.people),
              title: const Text('Profil Saya'),
              body: const SizedBox.shrink(),
              onTap: () {
                _logger.d('Profile tapped');
                widget.onProfileTap?.call();
              },
            ),
            PaneItem(
              icon: const Icon(FluentIcons.settings),
              title: const Text('Pengaturan Aplikasi'),
              body: const SizedBox.shrink(),
              onTap: () {
                _logger.d('Settings tapped');
                widget.onSettingsTap?.call();
              },
            ),
          ],
        ),
        
        // Standard footer items
        PaneItem(
          icon: const Icon(FluentIcons.help),
          title: const Text('Bantuan'),
          body: const SizedBox.shrink(),
          onTap: () {
            _logger.d('Help tapped');
          },
        ),
        PaneItem(
          icon: const Icon(FluentIcons.sign_out),
          title: const Text('Keluar'),
          body: const SizedBox.shrink(),
          onTap: () {
            _logger.d('Logout tapped');
            widget.onLogoutTap?.call();
          },
        ),
      ]);
    }
    
    return footerItems;
  }

  // ===== ROLE-BASED NAVIGATION ITEMS =====

  /// Admin Yayasan navigation items
  List<NavigationPaneItem> _getAdminYayasanItems() {
    final menuItems = AdminYayasanMenuStructure.getMenuItems();
    
    return menuItems.map((menuItem) {
      return menuItem.toNavigationPaneItem(
        onRouteNavigate: (route) {
          _logger.d('Navigate to route: $route');
          // TODO: Implement navigation using AppRouter
        },
        onActionExecute: (action) {
          _logger.d('Execute action: $action');
          // Handle specific actions if needed
        },
      );
    }).toList();
  }

  /// Perwakilan Yayasan navigation items
  List<NavigationPaneItem> _getPerwakilanYayasanItems() {
    return [
      PaneItem(
        icon: const Icon(FluentIcons.home),
        title: const Text('Dashboard'),
        body: const SizedBox.shrink(),
      ),
      PaneItem(
        icon: const Icon(FluentIcons.clipboard_list),
        title: const Text('Verifikasi Laporan'),
        body: const SizedBox.shrink(),
      ),
      PaneItem(
        icon: const Icon(FluentIcons.search),
        title: const Text('Audit Digital'),
        body: const SizedBox.shrink(),
      ),
      PaneItem(
        icon: const Icon(FluentIcons.issue_tracking),
        title: const Text('Manajemen Insiden'),
        body: const SizedBox.shrink(),
      ),
      PaneItem(
        icon: const Icon(FluentIcons.analytics_report),
        title: const Text('Laporan Pengawasan'),
        body: const SizedBox.shrink(),
      ),
    ];
  }

  /// Kepala Dapur navigation items
  List<NavigationPaneItem> _getKepalaDapurItems() {
    return [
      PaneItem(
        icon: const Icon(FluentIcons.home),
        title: const Text('Dashboard'),
        body: const SizedBox.shrink(),
      ),
      PaneItem(
        icon: const Icon(FluentIcons.task_list),
        title: const Text('Operasional Harian'),
        body: const SizedBox.shrink(),
      ),
      PaneItem(
        icon: const Icon(FluentIcons.people),
        title: const Text('Manajemen Staff'),
        body: const SizedBox.shrink(),
      ),
      PaneItem(
        icon: const Icon(FluentIcons.build_queue),
        title: const Text('Kontrol Kualitas'),
        body: const SizedBox.shrink(),
      ),
      PaneItem(
        icon: const Icon(FluentIcons.package),
        title: const Text('Inventory'),
        body: const SizedBox.shrink(),
      ),
      PaneItem(
        icon: const Icon(FluentIcons.document_management),
        title: const Text('Laporan Harian'),
        body: const SizedBox.shrink(),
      ),
    ];
  }

  /// Ahli Gizi navigation items
  List<NavigationPaneItem> _getAhliGiziItems() {
    return [
      PaneItem(
        icon: const Icon(FluentIcons.home),
        title: const Text('Dashboard'),
        body: const SizedBox.shrink(),
      ),
      PaneItem(
        icon: const Icon(FluentIcons.calendar),
        title: const Text('Siklus Menu'),
        body: const SizedBox.shrink(),
      ),
      PaneItem(
        icon: const Icon(FluentIcons.health),
        title: const Text('Validasi Gizi'),
        body: const SizedBox.shrink(),
      ),
      PaneItem(
        icon: const Icon(FluentIcons.analytics_report),
        title: const Text('Analisis Nutrisi'),
        body: const SizedBox.shrink(),
      ),
      PaneItem(
        icon: const Icon(FluentIcons.clipboard_list),
        title: const Text('Laporan Gizi'),
        body: const SizedBox.shrink(),
      ),
    ];
  }

  /// Akuntan navigation items
  List<NavigationPaneItem> _getAkuntanItems() {
    return [
      PaneItem(
        icon: const Icon(FluentIcons.home),
        title: const Text('Dashboard'),
        body: const SizedBox.shrink(),
      ),
      PaneItem(
        icon: const Icon(FluentIcons.money),
        title: const Text('Pencatatan Transaksi'),
        body: const SizedBox.shrink(),
      ),
      PaneItem(
        icon: const Icon(FluentIcons.document),
        title: const Text('Rekonsiliasi'),
        body: const SizedBox.shrink(),
      ),
      PaneItem(
        icon: const Icon(FluentIcons.analytics_report),
        title: const Text('Laporan Keuangan'),
        body: const SizedBox.shrink(),
      ),
      PaneItem(
        icon: const Icon(FluentIcons.financial),
        title: const Text('Anggaran SPPG'),
        body: const SizedBox.shrink(),
      ),
    ];
  }

  /// Pengawas Logistik navigation items
  List<NavigationPaneItem> _getPengawasLogistikItems() {
    return [
      PaneItem(
        icon: const Icon(FluentIcons.home),
        title: const Text('Dashboard'),
        body: const SizedBox.shrink(),
      ),
      PaneItem(
        icon: const Icon(FluentIcons.delivery_truck),
        title: const Text('Tracking Distribusi'),
        body: const SizedBox.shrink(),
      ),
      PaneItem(
        icon: const Icon(FluentIcons.car),
        title: const Text('Manajemen Armada'),
        body: const SizedBox.shrink(),
      ),
      PaneItem(
        icon: const Icon(FluentIcons.settings),
        title: const Text('Pemeliharaan'),
        body: const SizedBox.shrink(),
      ),
      PaneItem(
        icon: const Icon(FluentIcons.analytics_report),
        title: const Text('Laporan Logistik'),
        body: const SizedBox.shrink(),
      ),
    ];
  }

  /// Default navigation items
  List<NavigationPaneItem> _getDefaultItems() {
    return [
      PaneItem(
        icon: const Icon(FluentIcons.home),
        title: const Text('Dashboard'),
        body: const SizedBox.shrink(),
      ),
      PaneItem(
        icon: const Icon(FluentIcons.contact),
        title: const Text('Profil'),
        body: const SizedBox.shrink(),
      ),
    ];
  }

  // ===== HELPER METHODS =====

  /// Get role-specific color
  Color _getRoleColor(String role) {
    switch (role) {
      case 'admin_yayasan':
        return AppColors.dangerRed;
      case 'perwakilan_yayasan':
        return AppColors.warningOrange;
      case 'kepala_dapur':
        return AppColors.primary;
      case 'ahli_gizi':
        return AppColors.successGreen;
      case 'akuntan':
        return AppColors.infoBlue;
      case 'pengawas_logistik':
        return AppColors.neutralGray600;
      default:
        return AppColors.neutralGray500;
    }
  }

  /// Get role display name
  String _getRoleDisplayName(String role) {
    switch (role) {
      case 'admin_yayasan':
        return 'Admin Yayasan';
      case 'perwakilan_yayasan':
        return 'Perwakilan Yayasan';
      case 'kepala_dapur':
        return 'Kepala Dapur SPPG';
      case 'ahli_gizi':
        return 'Ahli Gizi';
      case 'akuntan':
        return 'Akuntan';
      case 'pengawas_logistik':
        return 'Pengawas Logistik';
      default:
        return 'User';
    }
  }
}

/// Navigation Pane Extensions
extension AppNavigationPaneExtensions on NavigationPane {
  /// Add custom styling
  NavigationPane withCustomStyling() {
    return NavigationPane(
      size: size,
      header: header,
      displayMode: displayMode,
      selected: selected,
      onChanged: onChanged,
      items: items,
      footerItems: footerItems,
      menuButton: menuButton,
      scrollController: scrollController,
      indicator: indicator,
      customPane: customPane,
      toggleable: toggleable,
      autoSuggestBox: autoSuggestBox,
      autoSuggestBoxReplacement: autoSuggestBoxReplacement,
      key: key,
    );
  }
}
