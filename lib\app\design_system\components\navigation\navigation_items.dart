import 'package:flutter/material.dart';
import '../../../../core/permissions/permission_mixins.dart';
import '../../../config/app_router.dart';
import 'app_bottom_navigation.dart';
import 'app_sidebar.dart';
import 'responsive_app_bar.dart';

/// Centralized navigation items configuration for the SOD-MBG application
/// Provides consistent navigation items across different navigation components
class NavigationItems {
  NavigationItems._();

  /// Get bottom navigation items for mobile layout
  static List<AppBottomNavigationItem> getBottomNavigationItems() {
    return [
      AppBottomNavigationItem(
        icon: const Icon(Icons.dashboard_outlined),
        selectedIcon: const Icon(Icons.dashboard),
        label: 'Dashboard',
        tooltip: 'Dashboard Utama',
        routePath: AppRouter.dashboard,
      ),
      AppBottomNavigationItem(
        icon: const Icon(Icons.kitchen_outlined),
        selectedIcon: const Icon(Icons.kitchen),
        label: 'Dapur',
        tooltip: 'Manajemen Dapur',
        routePath: AppRouter.kitchenManagement,
        requiredPermission: KitchenManagementPermission,
      ),
      AppBottomNavigationItem(
        icon: const Icon(Icons.inventory_2_outlined),
        selectedIcon: const Icon(Icons.inventory_2),
        label: 'Inventori',
        tooltip: 'Manajemen Inventori',
        routePath: AppRouter.inventory,
        requiredPermission: KitchenManagementPermission,
      ),
      AppBottomNavigationItem(
        icon: const Icon(Icons.local_shipping_outlined),
        selectedIcon: const Icon(Icons.local_shipping),
        label: 'Logistik',
        tooltip: 'Manajemen Logistik',
        routePath: AppRouter.logistics,
        requiredPermission: LogisticsPermission,
      ),
      AppBottomNavigationItem(
        icon: const Icon(Icons.assessment_outlined),
        selectedIcon: const Icon(Icons.assessment),
        label: 'Laporan',
        tooltip: 'Laporan dan Analisis',
        routePath: AppRouter.reporting,
      ),
    ];
  }

  /// Get sidebar navigation items for desktop layout
  static List<AppSidebarItem> getSidebarNavigationItems() {
    return [
      AppSidebarItem(
        icon: const Icon(Icons.dashboard_outlined),
        selectedIcon: const Icon(Icons.dashboard),
        label: 'Dashboard',
      ),
      AppSidebarItem(
        icon: const Icon(Icons.kitchen_outlined),
        selectedIcon: const Icon(Icons.kitchen),
        label: 'Manajemen Dapur',
        requiredPermission: KitchenManagementPermission,
      ),
      AppSidebarItem(
        icon: const Icon(Icons.restaurant_menu_outlined),
        selectedIcon: const Icon(Icons.restaurant_menu),
        label: 'Perencanaan Menu',
        requiredPermission: NutritionPermission,
      ),
      AppSidebarItem(
        icon: const Icon(Icons.inventory_2_outlined),
        selectedIcon: const Icon(Icons.inventory_2),
        label: 'Inventori',
        requiredPermission: KitchenManagementPermission,
      ),
      AppSidebarItem(
        icon: const Icon(Icons.local_shipping_outlined),
        selectedIcon: const Icon(Icons.local_shipping),
        label: 'Logistik',
        requiredPermission: LogisticsPermission,
      ),
      AppSidebarItem(
        icon: const Icon(Icons.account_balance_wallet_outlined),
        selectedIcon: const Icon(Icons.account_balance_wallet),
        label: 'Keuangan',
        requiredPermission: FinancialPermission,
      ),
      AppSidebarItem(
        icon: const Icon(Icons.assessment_outlined),
        selectedIcon: const Icon(Icons.assessment),
        label: 'Laporan',
      ),
      const AppSidebarItem.divider(),
      AppSidebarItem(
        icon: const Icon(Icons.admin_panel_settings_outlined),
        selectedIcon: const Icon(Icons.admin_panel_settings),
        label: 'Manajemen SPPG',
        requiredPermission: AdminPermission,
      ),
      AppSidebarItem(
        icon: const Icon(Icons.people_outlined),
        selectedIcon: const Icon(Icons.people),
        label: 'Manajemen Pengguna',
        requiredPermission: AdminPermission,
      ),
      AppSidebarItem(
        icon: const Icon(Icons.settings_outlined),
        selectedIcon: const Icon(Icons.settings),
        label: 'Pengaturan',
      ),
    ];
  }

  /// Get app bar navigation items for desktop layout
  static List<ResponsiveAppBarItem> getAppBarNavigationItems() {
    return [
      const ResponsiveAppBarItem(
        label: 'Dashboard',
        icon: Icons.dashboard_outlined,
      ),
      const ResponsiveAppBarItem(
        label: 'Dapur',
        icon: Icons.kitchen_outlined,
        permissionType: KitchenManagementPermission,
      ),
      const ResponsiveAppBarItem(
        label: 'Inventori',
        icon: Icons.inventory_2_outlined,
        permissionType: KitchenManagementPermission,
      ),
      const ResponsiveAppBarItem(
        label: 'Logistik',
        icon: Icons.local_shipping_outlined,
        permissionType: LogisticsPermission,
      ),
      const ResponsiveAppBarItem(
        label: 'Keuangan',
        icon: Icons.account_balance_wallet_outlined,
        permissionType: FinancialPermission,
      ),
      const ResponsiveAppBarItem(
        label: 'Laporan',
        icon: Icons.assessment_outlined,
      ),
    ];
  }

  /// Get navigation items with badge counts
  /// This would typically be called from a BLoC or state management solution
  static List<AppBottomNavigationItem> getBottomNavigationItemsWithBadges({
    int? kitchenNotifications,
    int? inventoryAlerts,
    int? logisticsUpdates,
    int? reportsPending,
  }) {
    final items = getBottomNavigationItems();

    return items.map((item) {
      switch (item.routePath) {
        case AppRouter.kitchenManagement:
          return item.copyWith(badgeCount: kitchenNotifications);
        case AppRouter.inventory:
          return item.copyWith(badgeCount: inventoryAlerts);
        case AppRouter.logistics:
          return item.copyWith(badgeCount: logisticsUpdates);
        case AppRouter.reporting:
          return item.copyWith(badgeCount: reportsPending);
        default:
          return item;
      }
    }).toList();
  }

  /// Get current navigation index based on route path
  static int getCurrentNavigationIndex(String currentRoute) {
    final items = getBottomNavigationItems();

    for (int i = 0; i < items.length; i++) {
      if (items[i].routePath == currentRoute) {
        return i;
      }
    }

    // Default to dashboard if route not found
    return 0;
  }

  /// Get route path for navigation index
  static String? getRoutePathForIndex(int index) {
    final items = getBottomNavigationItems();

    if (index >= 0 && index < items.length) {
      return items[index].routePath;
    }

    return null;
  }
}
