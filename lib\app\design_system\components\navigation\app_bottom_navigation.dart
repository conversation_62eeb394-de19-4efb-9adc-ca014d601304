import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../tokens/breakpoints.dart';
import '../../../../core/permissions/permission_mixins.dart';

/// A bottom navigation bar component for mobile layouts
/// Implements Material Design 3 NavigationBar with role-based menu items
/// Maximum of 5 items with badge support for notifications
class AppBottomNavigation extends StatelessWidget {
  /// Current selected index
  final int selectedIndex;

  /// Callback when an item is selected
  final ValueChanged<int>? onDestinationSelected;

  /// List of navigation items to display (max 5)
  final List<AppBottomNavigationItem> items;

  /// Background color of the bottom navigation
  final Color? backgroundColor;

  /// Height of the bottom navigation bar
  final double height;

  /// Whether to show labels on all items
  final bool showLabels;

  /// Animation duration for selection changes
  final Duration animationDuration;

  const AppBottomNavigation({
    super.key,
    required this.selectedIndex,
    required this.items,
    this.onDestinationSelected,
    this.backgroundColor,
    this.height = 80.0,
    this.showLabels = true,
    this.animationDuration = const Duration(milliseconds: 200),
  }) : assert(items.length <= 5, 'Maximum 5 navigation items allowed');

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final mediaQuery = MediaQuery.of(context);

    // Only show on mobile devices (< 600px)
    if (!AppBreakpoints.isMobile(mediaQuery.size.width)) {
      return const SizedBox.shrink();
    }

    // Filter items based on permissions
    final visibleItems =
        items.where((item) => _shouldShowItem(context, item)).toList();

    if (visibleItems.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      height: height,
      decoration: BoxDecoration(
        color: backgroundColor ?? colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: NavigationBar(
          selectedIndex: selectedIndex.clamp(0, visibleItems.length - 1),
          onDestinationSelected: onDestinationSelected,
          backgroundColor: Colors.transparent,
          elevation: 0,
          indicatorColor: colorScheme.secondaryContainer,
          labelBehavior:
              showLabels
                  ? NavigationDestinationLabelBehavior.alwaysShow
                  : NavigationDestinationLabelBehavior.onlyShowSelected,
          animationDuration: animationDuration,
          destinations: _buildNavigationDestinations(
            context,
            visibleItems,
            colorScheme,
          ),
        ),
      ),
    );
  }

  /// Builds navigation destinations from items
  List<NavigationDestination> _buildNavigationDestinations(
    BuildContext context,
    List<AppBottomNavigationItem> visibleItems,
    ColorScheme colorScheme,
  ) {
    return visibleItems.map((item) {
      return NavigationDestination(
        icon: _buildIconWithBadge(item, colorScheme, false),
        selectedIcon: _buildIconWithBadge(item, colorScheme, true),
        label: item.label,
        tooltip: item.tooltip ?? item.label,
      );
    }).toList();
  }

  /// Builds icon with optional badge
  Widget _buildIconWithBadge(
    AppBottomNavigationItem item,
    ColorScheme colorScheme,
    bool isSelected,
  ) {
    final icon =
        isSelected && item.selectedIcon != null
            ? item.selectedIcon!
            : item.icon;

    if (item.badgeCount == null || item.badgeCount! <= 0) {
      return icon;
    }

    return Badge(
      label: Text(
        item.badgeCount! > 99 ? '99+' : item.badgeCount.toString(),
        style: const TextStyle(fontSize: 10, fontWeight: FontWeight.w600),
      ),
      backgroundColor: colorScheme.error,
      textColor: colorScheme.onError,
      child: icon,
    );
  }

  /// Determines if an item should be shown based on permissions
  bool _shouldShowItem(BuildContext context, AppBottomNavigationItem item) {
    if (item.requiredPermission == null) {
      return true;
    }

    try {
      // Check permissions based on the required permission type
      if (item.requiredPermission == AdminPermission) {
        return context.hasPermission<AdminPermission>();
      } else if (item.requiredPermission == OversightPermission) {
        return context.hasPermission<OversightPermission>();
      } else if (item.requiredPermission == KitchenManagementPermission) {
        return context.hasPermission<KitchenManagementPermission>();
      } else if (item.requiredPermission == NutritionPermission) {
        return context.hasPermission<NutritionPermission>();
      } else if (item.requiredPermission == FinancialPermission) {
        return context.hasPermission<FinancialPermission>();
      } else if (item.requiredPermission == LogisticsPermission) {
        return context.hasPermission<LogisticsPermission>();
      }
    } catch (e) {
      // In case of permission check failure (e.g., in tests), show the item
      return true;
    }

    return true;
  }
}

/// Represents a navigation item in the bottom navigation
class AppBottomNavigationItem {
  /// Icon to display
  final Widget icon;

  /// Selected icon to display (optional)
  final Widget? selectedIcon;

  /// Label text to display
  final String label;

  /// Tooltip text (optional, defaults to label)
  final String? tooltip;

  /// Required permission to view this item
  final Type? requiredPermission;

  /// Badge count to display (optional)
  final int? badgeCount;

  /// Route path for navigation
  final String? routePath;

  /// Callback when this item is tapped
  final VoidCallback? onTap;

  /// Constructor for a navigation item
  const AppBottomNavigationItem({
    required this.icon,
    required this.label,
    this.selectedIcon,
    this.tooltip,
    this.requiredPermission,
    this.badgeCount,
    this.routePath,
    this.onTap,
  });

  /// Creates a copy of this item with updated properties
  AppBottomNavigationItem copyWith({
    Widget? icon,
    Widget? selectedIcon,
    String? label,
    String? tooltip,
    Type? requiredPermission,
    int? badgeCount,
    String? routePath,
    VoidCallback? onTap,
  }) {
    return AppBottomNavigationItem(
      icon: icon ?? this.icon,
      selectedIcon: selectedIcon ?? this.selectedIcon,
      label: label ?? this.label,
      tooltip: tooltip ?? this.tooltip,
      requiredPermission: requiredPermission ?? this.requiredPermission,
      badgeCount: badgeCount ?? this.badgeCount,
      routePath: routePath ?? this.routePath,
      onTap: onTap ?? this.onTap,
    );
  }
}

/// A controller for managing bottom navigation state and routing
class AppBottomNavigationController {
  /// List of navigation items
  final List<AppBottomNavigationItem> items;

  /// Current selected index
  int _selectedIndex = 0;

  /// Callback for index changes
  final ValueChanged<int>? onIndexChanged;

  AppBottomNavigationController({
    required this.items,
    this.onIndexChanged,
    int initialIndex = 0,
  }) : _selectedIndex =
           items.isEmpty ? 0 : initialIndex.clamp(0, items.length - 1);

  /// Get current selected index
  int get selectedIndex => _selectedIndex;

  /// Set selected index
  set selectedIndex(int index) {
    if (index >= 0 && index < items.length) {
      _selectedIndex = index;
      onIndexChanged?.call(_selectedIndex);
    }
  }

  /// Navigate to a specific index
  void navigateToIndex(BuildContext context, int index) {
    if (index >= 0 && index < items.length) {
      final item = items[index];

      // Execute custom onTap if provided
      if (item.onTap != null) {
        item.onTap!();
        return;
      }

      // Navigate using route path if provided
      if (item.routePath != null) {
        context.go(item.routePath!);
      }

      selectedIndex = index;
    }
  }

  /// Get current route path
  String? get currentRoutePath {
    if (_selectedIndex >= 0 && _selectedIndex < items.length) {
      return items[_selectedIndex].routePath;
    }
    return null;
  }

  /// Update badge count for a specific item
  void updateBadgeCount(int index, int? badgeCount) {
    if (index >= 0 && index < items.length) {
      // Note: This would require the items list to be mutable
      // In practice, you might want to use a state management solution
      // like BLoC or Provider to handle badge updates
    }
  }
}

/// A widget that provides bottom navigation with routing integration
class AppBottomNavigationScaffold extends StatefulWidget {
  /// The main content to display
  final Widget body;

  /// Navigation items for the bottom navigation
  final List<AppBottomNavigationItem> navigationItems;

  /// Initial selected index
  final int initialIndex;

  /// AppBar to display at the top
  final PreferredSizeWidget? appBar;

  /// FloatingActionButton to display
  final Widget? floatingActionButton;

  /// FloatingActionButton location
  final FloatingActionButtonLocation? floatingActionButtonLocation;

  /// Whether to show the bottom navigation
  final bool showBottomNavigation;

  /// Custom bottom navigation widget
  final Widget? customBottomNavigation;

  /// Background color for the scaffold
  final Color? backgroundColor;

  /// Whether to resize to avoid bottom inset
  final bool resizeToAvoidBottomInset;

  const AppBottomNavigationScaffold({
    super.key,
    required this.body,
    required this.navigationItems,
    this.initialIndex = 0,
    this.appBar,
    this.floatingActionButton,
    this.floatingActionButtonLocation,
    this.showBottomNavigation = true,
    this.customBottomNavigation,
    this.backgroundColor,
    this.resizeToAvoidBottomInset = true,
  });

  @override
  State<AppBottomNavigationScaffold> createState() =>
      _AppBottomNavigationScaffoldState();
}

class _AppBottomNavigationScaffoldState
    extends State<AppBottomNavigationScaffold> {
  late AppBottomNavigationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AppBottomNavigationController(
      items: widget.navigationItems,
      initialIndex: widget.initialIndex,
      onIndexChanged: (index) {
        setState(() {});
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final showBottomNav =
        widget.showBottomNavigation &&
        AppBreakpoints.isMobile(mediaQuery.size.width);

    return Scaffold(
      appBar: widget.appBar,
      body: widget.body,
      floatingActionButton: widget.floatingActionButton,
      floatingActionButtonLocation: widget.floatingActionButtonLocation,
      backgroundColor: widget.backgroundColor,
      resizeToAvoidBottomInset: widget.resizeToAvoidBottomInset,
      bottomNavigationBar:
          showBottomNav
              ? (widget.customBottomNavigation ??
                  AppBottomNavigation(
                    selectedIndex: _controller.selectedIndex,
                    items: widget.navigationItems,
                    onDestinationSelected: (index) {
                      _controller.navigateToIndex(context, index);
                    },
                  ))
              : null,
    );
  }
}
