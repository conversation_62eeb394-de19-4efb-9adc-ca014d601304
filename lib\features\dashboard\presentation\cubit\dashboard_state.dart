part of 'dashboard_bloc.dart';

/// Base class for all dashboard states
abstract class DashboardState {
  const DashboardState();

  @override
  List<Object?> get props => [];
}

/// Initial state when dashboard is first created
class DashboardInitial extends DashboardState {}

/// State when dashboard is loading initial data
class DashboardLoading extends DashboardState {}

/// State when dashboard data is successfully loaded


/// State when dashboard encounters an error
class DashboardError extends DashboardState {
  final String message;
  final String? componentId;
  final bool isRetryable;

  const DashboardError({
    required this.message,
    this.componentId,
    this.isRetryable = true,
  });

  @override
  List<Object?> get props => [message, componentId, isRetryable];
}

/// State when dashboard is refreshing data


/// Enum for individual component loading states
enum ComponentLoadingState { initial, loading, loaded, error, refreshing }
