import 'package:flutter/material.dart';
import '../../theme/spacing.dart';
import '../../tokens/design_tokens.dart';
import '../../../../core/permissions/permission_mixins.dart';

/// A responsive app bar that adapts to different screen sizes
/// and shows navigation items based on user permissions
class ResponsiveAppBar extends StatelessWidget implements PreferredSizeWidget {
  /// Title of the app bar
  final String title;

  /// Actions to display on the right side of the app bar
  final List<Widget>? actions;

  /// Leading widget for the app bar
  final Widget? leading;

  /// Whether to show the back button
  final bool automaticallyImplyLeading;

  /// Navigation items to display in the app bar
  final List<ResponsiveAppBarItem> navigationItems;

  /// Callback when a navigation item is selected
  final Function(int)? onNavigationItemSelected;

  /// Currently selected navigation item index
  final int selectedIndex;

  /// Whether to center the title
  final bool centerTitle;

  /// Custom title widget
  final Widget? titleWidget;

  /// Whether to show the navigation items
  final bool showNavigationItems;

  /// Whether to show the title
  final bool showTitle;

  /// Custom bottom widget
  final PreferredSizeWidget? bottom;

  /// Constructor for ResponsiveAppBar
  const ResponsiveAppBar({
    super.key,
    this.title = '',
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.navigationItems = const [],
    this.onNavigationItemSelected,
    this.selectedIndex = 0,
    this.centerTitle = false,
    this.titleWidget,
    this.showNavigationItems = true,
    this.showTitle = true,
    this.bottom,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;
    final isDesktop = screenWidth >= 1200;

    // Filter navigation items based on user permissions
    final visibleNavigationItems =
        navigationItems
            .where((item) => _hasPermissionForItem(context, item))
            .toList();

    return AppBar(
      backgroundColor: colorScheme.surface,
      foregroundColor: colorScheme.onSurface,
      elevation: 0,
      scrolledUnderElevation: 1,
      centerTitle: centerTitle,
      automaticallyImplyLeading: automaticallyImplyLeading,
      leading: leading,
      title:
          showTitle
              ? (titleWidget ??
                  Text(title, style: theme.textTheme.headlineSmall))
              : null,
      actions: [
        if (showNavigationItems && isDesktop)
          ..._buildDesktopNavigationItems(
            context,
            visibleNavigationItems,
            colorScheme,
          ),
        if (actions != null) ...?actions,
      ],
      bottom: bottom,
    );
  }

  /// Build navigation items for desktop layout
  List<Widget> _buildDesktopNavigationItems(
    BuildContext context,
    List<ResponsiveAppBarItem> items,
    ColorScheme colorScheme,
  ) {
    return items.asMap().entries.map((entry) {
      final index = entry.key;
      final item = entry.value;
      final isSelected = index == selectedIndex;

      return Padding(
        padding: AppSpacing.horizontalSm,
        child: TextButton(
          onPressed: () {
            if (onNavigationItemSelected != null) {
              onNavigationItemSelected!(index);
            }
          },
          style: TextButton.styleFrom(
            foregroundColor:
                isSelected ? colorScheme.primary : colorScheme.onSurface,
            backgroundColor:
                isSelected
                    ? colorScheme.primaryContainer.withValues(alpha: 0.1)
                    : Colors.transparent,
            padding: AppSpacing.horizontalMd,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (item.icon != null)
                Icon(item.icon, size: DesignTokens.iconSizes['sm']),
              if (item.icon != null) AppSpacing.horizontalSpaceXs,
              Text(item.label),
            ],
          ),
        ),
      );
    }).toList();
  }

  /// Check if user has permission for a navigation item
  bool _hasPermissionForItem(BuildContext context, ResponsiveAppBarItem item) {
    if (item.permissionType == null) {
      return true; // No permission required
    }

    if (item.permissionType == AdminPermission) {
      return context.hasPermission<AdminPermission>();
    } else if (item.permissionType == OversightPermission) {
      return context.hasPermission<OversightPermission>();
    } else if (item.permissionType == KitchenManagementPermission) {
      return context.hasPermission<KitchenManagementPermission>();
    } else if (item.permissionType == NutritionPermission) {
      return context.hasPermission<NutritionPermission>();
    } else if (item.permissionType == FinancialPermission) {
      return context.hasPermission<FinancialPermission>();
    } else if (item.permissionType == LogisticsPermission) {
      return context.hasPermission<LogisticsPermission>();
    }

    return false;
  }

  @override
  Size get preferredSize =>
      Size.fromHeight(kToolbarHeight + (bottom?.preferredSize.height ?? 0));
}

/// Model class for navigation items in the ResponsiveAppBar
class ResponsiveAppBarItem {
  /// Label for the navigation item
  final String label;

  /// Icon for the navigation item
  final IconData? icon;

  /// Permission type required to see this navigation item
  final Type? permissionType;

  /// Constructor for ResponsiveAppBarItem
  const ResponsiveAppBarItem({
    required this.label,
    this.icon,
    this.permissionType,
  });
}
