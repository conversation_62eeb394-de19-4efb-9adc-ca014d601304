import 'package:flutter/material.dart';

/// Design tokens for SOD-MBG application
/// Contains all design constants including colors, sizes, and spacing values
class DesignTokens {
  DesignTokens._();

  // ============================================================================
  // COLOR TOKENS
  // ============================================================================

  /// Primary color palette
  static const Map<String, Color> primaryColors = {
    'green': Color(0xFF2E7D32),
    'green-light': Color(0xFF60AD5E),
    'green-dark': Color(0xFF005005),
  };

  /// Secondary color palette
  static const Map<String, Color> secondaryColors = {
    'blue': Color(0xFF1976D2),
    'blue-light': Color(0xFF63A4FF),
    'blue-dark': Color(0xFF004BA0),
  };

  /// Status color palette
  static const Map<String, Color> statusColors = {
    'success': Color(0xFF4CAF50),
    'warning': Color(0xFFFF9800),
    'error': Color(0xFFF44336),
    'info': Color(0xFF2196F3),
  };

  /// Neutral color palette
  static const Map<String, Color> neutralColors = {
    'white': Color(0xFFFFFFFF),
    'black': Color(0xFF000000),
    'grey-50': Color(0xFFFAFAFA),
    'grey-100': Color(0xFFF5F5F5),
    'grey-200': Color(0xFFEEEEEE),
    'grey-300': Color(0xFFE0E0E0),
    'grey-400': Color(0xFFBDBDBD),
    'grey-500': Color(0xFF9E9E9E),
    'grey-600': Color(0xFF757575),
    'grey-700': Color(0xFF616161),
    'grey-800': Color(0xFF424242),
    'grey-900': Color(0xFF212121),
  };

  /// Surface color palette
  static const Map<String, Color> surfaceColors = {
    'surface-light': Color(0xFFFAFAFA),
    'surface-dark': Color(0xFF121212),
    'surface-variant-light': Color(0xFFF5F5F5),
    'surface-variant-dark': Color(0xFF2C2C2C),
  };

  // ============================================================================
  // SIZE TOKENS
  // ============================================================================

  /// Icon sizes
  static const Map<String, double> iconSizes = {
    'xs': 12.0,
    'sm': 16.0,
    'md': 24.0,
    'lg': 32.0,
    'xl': 48.0,
  };

  /// Button sizes
  static const Map<String, Size> buttonSizes = {
    'sm': Size(80, 32),
    'md': Size(120, 48),
    'lg': Size(160, 56),
  };

  /// Avatar sizes
  static const Map<String, double> avatarSizes = {
    'xs': 24.0,
    'sm': 32.0,
    'md': 40.0,
    'lg': 56.0,
    'xl': 80.0,
  };

  /// Input field sizes
  static const Map<String, double> inputSizes = {
    'sm': 32.0,
    'md': 48.0,
    'lg': 56.0,
  };

  // ============================================================================
  // SPACING TOKENS
  // ============================================================================

  /// Base spacing unit (8px grid system)
  static const double baseSpacing = 8.0;

  /// Spacing scale
  static const Map<String, double> spacing = {
    'xs': 4.0, // 0.5 * baseSpacing
    'sm': 8.0, // 1.0 * baseSpacing
    'md': 16.0, // 2.0 * baseSpacing
    'lg': 24.0, // 3.0 * baseSpacing
    'xl': 32.0, // 4.0 * baseSpacing
    'xxl': 48.0, // 6.0 * baseSpacing
    'xxxl': 64.0, // 8.0 * baseSpacing
  };

  /// Component-specific spacing
  static const Map<String, double> componentSpacing = {
    'card-padding': 16.0,
    'card-margin': 8.0,
    'button-padding': 16.0,
    'form-field-spacing': 16.0,
    'section-spacing': 24.0,
    'page-margin': 16.0,
    'screen-padding': 16.0,
    'content-spacing': 24.0,
    'list-item-spacing': 8.0,
    'grid-spacing': 16.0,
    'navigation-padding': 8.0,
    'navigation-item-spacing': 4.0,
    'app-bar-padding': 16.0,
  };

  // ============================================================================
  // TYPOGRAPHY TOKENS
  // ============================================================================

  /// Font family
  static const String fontFamily = 'Roboto';

  /// Font sizes
  static const Map<String, double> fontSizes = {
    'xs': 10.0,
    'sm': 12.0,
    'md': 14.0,
    'lg': 16.0,
    'xl': 18.0,
    'xxl': 20.0,
    'xxxl': 24.0,
    'display-sm': 24.0,
    'display-md': 28.0,
    'display-lg': 32.0,
  };

  /// Font weights
  static const Map<String, FontWeight> fontWeights = {
    'light': FontWeight.w300,
    'regular': FontWeight.w400,
    'medium': FontWeight.w500,
    'semibold': FontWeight.w600,
    'bold': FontWeight.w700,
  };

  /// Line heights
  static const Map<String, double> lineHeights = {
    'tight': 1.1,
    'normal': 1.2,
    'relaxed': 1.3,
    'loose': 1.4,
    'extra-loose': 1.5,
  };

  /// Letter spacing
  static const Map<String, double> letterSpacing = {
    'tight': -0.5,
    'normal': 0.0,
    'wide': 0.1,
    'wider': 0.15,
    'widest': 0.25,
    'extra-wide': 0.4,
    'super-wide': 0.5,
  };

  // ============================================================================
  // BORDER RADIUS TOKENS
  // ============================================================================

  /// Border radius values
  static const Map<String, double> borderRadius = {
    'none': 0.0,
    'xs': 2.0,
    'sm': 4.0,
    'md': 8.0,
    'lg': 12.0,
    'xl': 16.0,
    'xxl': 24.0,
    'full': 9999.0,
  };

  // ============================================================================
  // ELEVATION TOKENS
  // ============================================================================

  /// Elevation values for Material Design
  static const Map<String, double> elevation = {
    'none': 0.0,
    'xs': 1.0,
    'sm': 2.0,
    'md': 4.0,
    'lg': 8.0,
    'xl': 12.0,
    'xxl': 16.0,
    'xxxl': 24.0,
  };

  // ============================================================================
  // ANIMATION TOKENS
  // ============================================================================

  /// Animation durations
  static const Map<String, Duration> animationDuration = {
    'fast': Duration(milliseconds: 150),
    'normal': Duration(milliseconds: 300),
    'slow': Duration(milliseconds: 500),
    'slower': Duration(milliseconds: 750),
    'slowest': Duration(milliseconds: 1000),
  };

  /// Animation curves
  static const Map<String, Curve> animationCurves = {
    'linear': Curves.linear,
    'ease': Curves.ease,
    'ease-in': Curves.easeIn,
    'ease-out': Curves.easeOut,
    'ease-in-out': Curves.easeInOut,
    'bounce': Curves.bounceOut,
    'elastic': Curves.elasticOut,
  };

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  /// Get color by key
  static Color? getColor(String category, String key) {
    switch (category) {
      case 'primary':
        return primaryColors[key];
      case 'secondary':
        return secondaryColors[key];
      case 'status':
        return statusColors[key];
      case 'neutral':
        return neutralColors[key];
      case 'surface':
        return surfaceColors[key];
      default:
        return null;
    }
  }

  /// Get spacing value by key
  static double getSpacing(String key) {
    return spacing[key] ?? 0.0;
  }

  /// Get component spacing value by key
  static double getComponentSpacing(String key) {
    return componentSpacing[key] ?? 0.0;
  }

  /// Get font size by key
  static double getFontSize(String key) {
    return fontSizes[key] ?? 14.0;
  }

  /// Get font weight by key
  static FontWeight getFontWeight(String key) {
    return fontWeights[key] ?? FontWeight.w400;
  }

  /// Get border radius by key
  static double getBorderRadius(String key) {
    return borderRadius[key] ?? 0.0;
  }

  /// Get elevation by key
  static double getElevation(String key) {
    return elevation[key] ?? 0.0;
  }

  /// Get animation duration by key
  static Duration getAnimationDuration(String key) {
    return animationDuration[key] ?? const Duration(milliseconds: 300);
  }

  /// Get animation curve by key
  static Curve getAnimationCurve(String key) {
    return animationCurves[key] ?? Curves.ease;
  }

  /// Get icon size by key
  static double getIconSize(String key) {
    return iconSizes[key] ?? 24.0;
  }

  /// Get button size by key
  static Size getButtonSize(String key) {
    return buttonSizes[key] ?? const Size(120, 48);
  }

  /// Get avatar size by key
  static double getAvatarSize(String key) {
    return avatarSizes[key] ?? 40.0;
  }

  /// Get input size by key
  static double getInputSize(String key) {
    return inputSizes[key] ?? 48.0;
  }
}
