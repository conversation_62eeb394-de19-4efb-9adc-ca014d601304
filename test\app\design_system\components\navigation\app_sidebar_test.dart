import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:aplikasi_sppg/app/design_system/components/navigation/app_sidebar.dart';
import 'package:mockito/mockito.dart';

// Mock BuildContext with permission extensions
class MockBuildContext extends Mock implements BuildContext {
  bool hasPermissionValue = true;
}

void main() {
  group('AppSidebar Widget Tests', () {
    testWidgets('renders correctly when expanded', (WidgetTester tester) async {
      // Arrange
      final items = [
        AppSidebarItem(icon: const Icon(Icons.home), label: 'Home'),
        AppSidebarItem(icon: const Icon(Icons.settings), label: 'Settings'),
      ];

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AppSidebar(selectedIndex: 0, items: items, isExpanded: true),
          ),
        ),
      );

      // Assert
      expect(find.byIcon(Icons.home), findsOneWidget);
      expect(find.byIcon(Icons.settings), findsOneWidget);
      expect(find.byIcon(Icons.chevron_left), findsOneWidget);
    });

    testWidgets('renders correctly when collapsed', (
      WidgetTester tester,
    ) async {
      // Arrange
      final items = [
        AppSidebarItem(icon: const Icon(Icons.home), label: 'Home'),
        AppSidebarItem(icon: const Icon(Icons.settings), label: 'Settings'),
      ];

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AppSidebar(selectedIndex: 0, items: items, isExpanded: false),
          ),
        ),
      );

      // Assert
      expect(find.byIcon(Icons.home), findsOneWidget);
      expect(find.byIcon(Icons.settings), findsOneWidget);
      expect(find.byIcon(Icons.chevron_right), findsOneWidget);
    });

    testWidgets('calls onExpandButtonPressed when expand button is pressed', (
      WidgetTester tester,
    ) async {
      // Arrange
      bool expandButtonPressed = false;
      final items = [
        AppSidebarItem(icon: const Icon(Icons.home), label: 'Home'),
      ];

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AppSidebar(
              selectedIndex: 0,
              items: items,
              isExpanded: true,
              onExpandButtonPressed: () {
                expandButtonPressed = true;
              },
            ),
          ),
        ),
      );

      await tester.tap(find.byIcon(Icons.chevron_left));
      await tester.pump();

      // Assert
      expect(expandButtonPressed, isTrue);
    });

    testWidgets('calls onDestinationSelected when item is tapped', (
      WidgetTester tester,
    ) async {
      // Arrange
      int selectedIndex = -1;
      final items = [
        AppSidebarItem(icon: const Icon(Icons.home), label: 'Home'),
        AppSidebarItem(icon: const Icon(Icons.settings), label: 'Settings'),
      ];

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AppSidebar(
              selectedIndex: 0,
              items: items,
              onDestinationSelected: (index) {
                selectedIndex = index;
              },
            ),
          ),
        ),
      );

      await tester.tap(find.byIcon(Icons.settings));
      await tester.pump();

      // Assert
      expect(selectedIndex, 1);
    });

    testWidgets('renders header and trailing widgets when provided', (
      WidgetTester tester,
    ) async {
      // Arrange
      final items = [
        AppSidebarItem(icon: const Icon(Icons.home), label: 'Home'),
      ];

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AppSidebar(
              selectedIndex: 0,
              items: items,
              header: const Text('Header'),
              trailing: const Text('Trailing'),
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('Header'), findsOneWidget);
      expect(find.text('Trailing'), findsOneWidget);
    });
  });

  group('AppSidebarScaffold Widget Tests', () {
    testWidgets('renders correctly with sidebar and body', (
      WidgetTester tester,
    ) async {
      // Arrange
      final sidebar = AppSidebar(
        selectedIndex: 0,
        items: [AppSidebarItem(icon: const Icon(Icons.home), label: 'Home')],
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: AppSidebarScaffold(
            sidebar: sidebar,
            body: const Center(child: Text('Main Content')),
          ),
        ),
      );

      // Assert
      expect(find.byType(AppSidebar), findsOneWidget);
      expect(find.text('Main Content'), findsOneWidget);
    });

    testWidgets('hides sidebar when showSidebar is false', (
      WidgetTester tester,
    ) async {
      // Arrange
      final sidebar = AppSidebar(
        selectedIndex: 0,
        items: [AppSidebarItem(icon: const Icon(Icons.home), label: 'Home')],
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: AppSidebarScaffold(
            sidebar: sidebar,
            body: const Center(child: Text('Main Content')),
            showSidebar: false,
          ),
        ),
      );

      // Assert
      expect(find.byType(AppSidebar), findsNothing);
      expect(find.text('Main Content'), findsOneWidget);
    });

    testWidgets('renders appBar and floatingActionButton when provided', (
      WidgetTester tester,
    ) async {
      // Arrange
      final sidebar = AppSidebar(
        selectedIndex: 0,
        items: [AppSidebarItem(icon: const Icon(Icons.home), label: 'Home')],
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: AppSidebarScaffold(
            sidebar: sidebar,
            body: const Center(child: Text('Main Content')),
            appBar: AppBar(title: const Text('App Bar')),
            floatingActionButton: FloatingActionButton(
              onPressed: () {},
              child: const Icon(Icons.add),
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('App Bar'), findsOneWidget);
      expect(find.byIcon(Icons.add), findsOneWidget);
    });
  });
}
