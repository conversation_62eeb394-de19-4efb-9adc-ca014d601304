part of 'dashboard_bloc.dart';

/// Base class for all dashboard events
abstract class DashboardEvent  {
  const DashboardEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load initial dashboard data
class DashboardLoadRequested extends DashboardEvent {
  final String roleId;
  final String? sppgId;

  const DashboardLoadRequested({required this.roleId, this.sppgId});

  @override
  List<Object?> get props => [roleId, sppgId];
}

/// Event to refresh all dashboard data
class DashboardRefreshRequested extends DashboardEvent {
  final String? roleId;
  final String? sppgId;

  const DashboardRefreshRequested({this.roleId, this.sppgId});

  @override
  List<Object?> get props => [roleId, sppgId];
}

/// Event to load KPI data specifically
class KPIDataLoadRequested extends DashboardEvent {
  final String roleId;
  final String? sppgId;

  const KPIDataLoadRequested({required this.roleId, this.sppgId});

  @override
  List<Object?> get props => [roleId, sppgId];
}

/// Event to load pending actions
class PendingActionsLoadRequested extends DashboardEvent {
  final String roleId;
  final String? sppgId;

  const PendingActionsLoadRequested({required this.roleId, this.sppgId});

  @override
  List<Object?> get props => [roleId, sppgId];
}

/// Event to load SPPG locations
class SPPGLocationsLoadRequested extends DashboardEvent {
  final String roleId;

  const SPPGLocationsLoadRequested({required this.roleId});

  @override
  List<Object?> get props => [roleId];
}

/// Event to load performance data
class PerformanceDataLoadRequested extends DashboardEvent {
  final String roleId;
  final String? sppgId;
  final DateTime? startDate;
  final DateTime? endDate;

  const PerformanceDataLoadRequested({
    required this.roleId,
    this.sppgId,
    this.startDate,
    this.endDate,
  });

  @override
  List<Object?> get props => [roleId, sppgId, startDate, endDate];
}

/// Event to load activity events
class ActivityEventsLoadRequested extends DashboardEvent {
  final String roleId;
  final String? sppgId;
  final int? limit;

  const ActivityEventsLoadRequested({
    required this.roleId,
    this.sppgId,
    this.limit,
  });

  @override
  List<Object?> get props => [roleId, sppgId, limit];
}

/// Event for real-time activity updates
class ActivityEventReceived extends DashboardEvent {
  final String activityId;
  final String type;
  final String sppgName;
  final DateTime timestamp;
  final Map<String, dynamic> data;

  const ActivityEventReceived({
    required this.activityId,
    required this.type,
    required this.sppgName,
    required this.timestamp,
    required this.data,
  });

  @override
  List<Object?> get props => [activityId, type, sppgName, timestamp, data];
}

/// Event to handle component-specific errors
class DashboardComponentErrorOccurred extends DashboardEvent {
  final String componentId;
  final dynamic error;
  final bool isRetryable;

  const DashboardComponentErrorOccurred({
    required this.componentId,
    required this.error,
    this.isRetryable = true,
  });

  @override
  List<Object?> get props => [componentId, error, isRetryable];
}

/// Event to retry failed component loading
class DashboardComponentRetryRequested extends DashboardEvent {
  final String componentId;

  const DashboardComponentRetryRequested({required this.componentId});

  @override
  List<Object?> get props => [componentId];
}

/// Event to update dashboard configuration
class DashboardConfigurationUpdated extends DashboardEvent {
  final String roleId;

  const DashboardConfigurationUpdated({required this.roleId});

  @override
  List<Object?> get props => [roleId];
}


/// Event to request permission check for dashboard components
class DashboardPermissionCheckRequested extends DashboardEvent {
  const DashboardPermissionCheckRequested();

  @override
  List<Object?> get props => [];
}
